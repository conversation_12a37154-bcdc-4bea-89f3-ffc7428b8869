import { guard } from '@/plugins/guard'
import { fetchy } from '@/plugins/fetchy'
import { computed } from 'vue'
import { checkValue } from '@lib/scripts/utils'
import { SectionKeys } from '@/modules/inventory/types'
import { appMode, facilitiesList } from '@/store'

import type { InputType } from '@lib/components/inputs/input/types'
import type { DraftWidgets } from '@/types'
import type { CreateProductDetails } from '@/modules/inventory/views/create-product/types'

import type {
  AllProductProperties,
  BaseProduct,
  BulkProductsResponse,
  DraftProduct,
  InventoryParams,
  InventoryViewType,
  InventoryWidgets,
  KitPostData,
  LiveProduct,
  ProductDetailsSection
} from '@/modules/inventory/types'

export enum InventoryCountOperatorEnum {
  LESS = 'lt',
  EQUALS = 'eq',
  GREATER = 'gt'
}

export const inventoryCountOperatorList: DropListOption[] = [
  {
    id:   InventoryCountOperatorEnum.EQUALS,
    name: 'Equals'
  },
  {
    id:   InventoryCountOperatorEnum.LESS,
    name: 'Less Than'
  },
  {
    id:   InventoryCountOperatorEnum.GREATER,
    name: 'Greater Than'
  },
]

export const draftProductType: DropListOption[] = [
  {
    id:   'KIT',
    name: 'Kit'
  },
  {
    id:   'PHYSICAL',
    name: 'Individual'
  },
]

const isAdmin = computed(() => appMode === 'ADMIN' )

export function sanitizeKey( value: string | number | boolean, type: InputType | 'toggle' ) {

  let sValue = value

  if ( type === 'text' && !checkValue( sValue ))
    sValue = null

  if (( type === 'number' || type === 'currency' ) && !checkValue( sValue ))
    sValue = null

  if ( type === 'toggle' ) {
    if ( !value || value === null || value === undefined )
      sValue = false
  }

  return sValue

}

/**
 * Creates the sections for the product details.
 * @param {InventoryViewType} type - The type of view.
 * @param {string} facilityCode - The facility code.
 * @param {number} masterId - The master ID.
 * @returns {ProductDetailsSection[]} The product details sections.
 */
export function createSections(
  type: InventoryViewType,
  facilityCode: string,
  masterId?: number
): ProductDetailsSection[] {

  return [
    {
      key:         SectionKeys.generalInfo,
      name:        'General Information',
      hidden:      true,
      iconName:    'product',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'left',
      model:       [
        {
          to:          { name: 'Live Product Details', params: { productId: masterId } },
          key:         'masterRecordId',
          type:        'number',
          label:       'Product ID',
          valid:       true,
          class:       'lg:hidden',
          value:       masterId,
          hidden:      !masterId,
          required:    false,
          nonEditable: true,
        },
        {
          key:         'type',
          type:        'text',
          label:       'Product Type',
          value:       null,
          valid:       true,
          hidden:      type === 'live',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'isActive',
          type:        'toggle',
          label:       'Active Product',
          value:       false,
          valid:       false,
          required:    false,
          readonly:    false,
          nonEditable: false
        },
        {
          key:         'containsDocumentOnly',
          type:        'toggle',
          label:       'Contains Documents Only',
          value:       false,
          valid:       false,
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'sku',
          type:        'text',
          label:       'SKU',
          value:       '',
          valid:       false,
          required:    type === 'draft',
          nonEditable: type === 'live'
        },
        {
          key:      'title',
          type:     'textarea',
          label:    'Title',
          value:    '',
          valid:    false,
          required: true
        },
        {
          key:      'reorderAlertQuantity',
          type:     'number',
          label:    'Reorder Alert Quantity',
          valid:    false,
          value:    0,
          required: true
        },
        {
          key:      'description',
          type:     'textarea',
          label:    'Description',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'packingInstructions',
          type:     'textarea',
          label:    'Pack Instructions',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'retailPrice',
          type:     'currency',
          label:    'Retail Price',
          value:    0,
          valid:    false,
          suffix:   'USD',
          required: false
        },
        {
          key:         'defaultFacilityCode',
          type:        'select',
          label:       'Default Shipment Facility Code',
          value:       '',
          valid:       false,
          hidden:      !isAdmin.value,
          options:     facilitiesList.value,
          required:    false,
          nonEditable: true
        },
        {
          key:         null,
          type:        'textarea',
          label:       'Default Shipment Facility',
          value:       facilitiesList.value.find( f => f.id === facilityCode )?.name,
          valid:       false,
          hidden:      !isAdmin.value,
          nonEditable: true
        }
      ]
    },
    {
      key:         SectionKeys.attributes,
      name:        'Attributes',
      iconName:    'attributes',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'left',
      model:       [
        {
          key:      'color',
          type:     'text',
          label:    'Color',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'size',
          type:     'text',
          label:    'Size',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:         'cubicFeet',
          type:        'number',
          label:       'Cubic Feet',
          value:       0,
          valid:       false,
          hidden:      type === 'draft',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'length',
          type:        'number',
          label:       'Length',
          value:       0,
          valid:       false,
          hidden:      type === 'draft',
          suffix:      'in',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'width',
          type:        'number',
          label:       'Width',
          value:       0,
          valid:       false,
          hidden:      type === 'draft',
          suffix:      'in',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'height',
          type:        'number',
          label:       'Height',
          value:       0,
          valid:       false,
          hidden:      type === 'draft',
          suffix:      'in',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
        {
          key:         'weight',
          type:        'number',
          label:       'Weight',
          value:       0,
          valid:       false,
          hidden:      type === 'draft',
          suffix:      'lbs',
          required:    false,
          readonly:    true,
          nonEditable: true
        },
      ]
    },
    {
      key:         SectionKeys.otherDetails,
      name:        'Other Details',
      iconName:    'details',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'left',
      model:       [
        {
          key:         'isbn',
          type:        'text',
          label:       'ISBN',
          value:       '',
          valid:       false,
          required:    false,
          nonEditable: !guard( appMode )
        },
        {
          key:         'upc',
          type:        'text',
          label:       'UPC',
          value:       '',
          valid:       false,
          required:    false,
          nonEditable: !guard( appMode )
        },
        {
          key:      'groupCode',
          type:     'text',
          label:    'Group Code',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'keywords',
          type:     'text',
          label:    'Keyword',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'notes',
          type:     'textarea',
          label:    'Notes',
          value:    '',
          valid:    false,
          required: false
        }
      ]
    },
    {
      key:         SectionKeys.externalLinks,
      name:        'External Links',
      iconName:    'external-link',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'left',
      model:       [
        {
          to:           null,
          key:          'webUrl',
          type:         'text',
          label:        'Web URL',
          value:        '',
          valid:        false,
          required:     false,
          externalLink: true
        },
        {
          to:           null,
          key:          'imageUrl',
          type:         'text',
          label:        'Image URL',
          value:        '',
          valid:        false,
          required:     false,
          externalLink: true
        },
        {
          to:           null,
          key:          'imageThumbUrl',
          type:         'text',
          label:        'Image Thumbnail URL',
          value:        '',
          valid:        false,
          required:     false,
          externalLink: true
        }
      ]
    },
    {
      key:         SectionKeys.stock,
      name:        'Stock',
      hidden:      !guard( appMode ) && type === 'draft',
      editable:    guard( appMode ),
      iconName:    'portal-inventory',
      viewSection: 'right',
      model:       [
        {
          key:      'masterCaseQuantity',
          type:     'number',
          label:    'Master Case QTY',
          value:    0,
          valid:    false,
          hidden:   !guard( appMode ),
          required: false,
        },
        {
          key:      'caseQuantity',
          type:     'number',
          label:    'Case QTY',
          value:    0,
          valid:    false,
          required: false
        }
      ]
    },
    {
      key:         SectionKeys.supplier,
      name:        'Supplier',
      iconName:    'supplier',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'right',
      model:       [
        {
          key:      'supplier',
          label:    'Supplier',
          type:     'text',
          value:    '',
          valid:    false,
          required: false
        },
        {
          key:      'supplierCost',
          label:    'Supplier Cost',
          type:     'currency',
          value:    0,
          valid:    false,
          suffix:   'USD',
          required: false
        },
        {
          key:      'supplierSku',
          label:    'Supplier SKU',
          type:     'text',
          value:    '',
          valid:    false,
          required: false
        }
      ]
    },
    {
      key:         SectionKeys.customs,
      name:        'Customs',
      iconName:    'customs',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'right',
      model:       [
        {
          key:      'customsValue',
          label:    'Customs Value',
          type:     'currency',
          value:    0,
          valid:    false,
          suffix:   'USD',
          required: false
        },
        {
          key:      'customsDescription',
          label:    'Customs Description',
          type:     'textarea',
          value:    '',
          valid:    false,
          required: false
        }
      ]
    },
    {
      key:         SectionKeys.bulk,
      name:        'Bulk Options',
      hidden:      true,
      iconName:    'bulk',
      editable:    guard( 'Inventory.Write' ),
      viewSection: 'right',
      model:       [
        {
          key:      'isBulkyPick',
          label:    'Bulk Pick',
          type:     'toggle',
          value:    false,
          valid:    false,
          required: false,
        },
        {
          key:      'shipSystemWeight',
          label:    'Ship System Weight',
          type:     'toggle',
          value:    false,
          valid:    false,
          required: false
        },
        {
          key:      'isInsert',
          label:    'Is Insert',
          type:     'toggle',
          value:    false,
          valid:    false,
          required: false
        },
        {
          key:      'isBulkyPack',
          label:    'Bulk Pack',
          type:     'toggle',
          value:    false,
          valid:    false,
          required: false,
        }
      ]
    },
    {
      key:         SectionKeys.errors,
      name:        'Resolve Errors',
      hidden:      true,
      iconName:    'issue-circle',
      viewSection: 'left',
      model:       []
    }
  ]

}

/**
 * Map the product details to the sections
 * @param {ProductDetailsSection[]} sections - the sections to map in
 * @param {ProductType extends Partial<AllProductProperties>} details - the product details
 * @returns {ProductDetailsSection[]} Mapped sections
 */
export function mapDetailsToSections<ProductType extends Partial<AllProductProperties>>(
  sections: ProductDetailsSection[],
  details: ProductType
): ProductDetailsSection[] {

  sections.forEach(( section ) => {

    section.model.forEach(( item ) => {

      if ( item.key in details ) {

        item.value // ------------------------------ If the value is a number, check if it is a null
          = [ 'number', 'strict-number' ].includes( item.type )
            ? checkValue( details[item.key] )
              ? details[item.key]
              : null
            : details[item.key]

        item.declaredValue // ---------------------- If the declared value is a number, check if it is a null
          = [ 'number', 'strict-number' ].includes( item.type )
            ? checkValue( details[item.key] )
              ? details[item.key]
              : null
            : details[item.key]

        if ( item?.externalLink ) // --------------- If the item is an external link, set the item.to value to the link.
          item.to = checkValue( details[item.key] ) ? String( details[item.key] ) : null

      }

      if ( details?.importErrorCount > 0 ) { // ---- If there are import errors, find the error for the item

        const error = details.importErrors.find( e => e.propertyName === item.key )

        if ( error )
          item.error = error.errorMsg

      }

      else {
        item.error = null
      }

    })

  })

  return sections

}

export async function getProducts( params: InventoryParams ) {

  return await fetchy<PaginatedResponse<'items', BaseProduct[]>>({
    url:    `products`,
    method: 'GET',
    params
  })

}

export async function getProduct( productId: number ) {

  return await fetchy<LiveProduct>({
    url:    `products/${productId}`,
    method: 'GET'
  })

}

export async function exportLiveProduct( productId: number ) {

  return fetchy<ArrayBuffer>({
    url:          `export/products/${productId}/excel`,
    method:       'GET',
    responseType: 'blob'
  })

}

export async function updateLiveProduct( data: LiveProduct, productId: number ) {

  return fetchy<LiveProduct>({
    url:    `products/${productId}`,
    method: 'PUT',
    data
  })

}

export async function bulkExportProducts( productIds: number[], isImportExport: boolean = false ) {

  return fetchy<Blob>({
    url:          `export/products?isImportExport=${isImportExport}`,
    data:         { productIds },
    method:       'POST',
    responseType: 'blob',
  })

}

export async function bulkDeleteDraftProducts( stagingProductIdList: number[] ) {

  return fetchy({
    url:    `stage/products/bulk-operations/delete`,
    data:   { stagingProductIdList },
    method: 'POST'
  })
}

export async function getDraftProducts( params: InventoryParams ) {

  return await fetchy<PaginatedResponse<'stageProducts', DraftProduct[]>>({
    url:    `stage/products`,
    method: 'GET',
    params
  })

}

export async function getDraftProduct( productId: number ) {

  return await fetchy<DraftProduct>({
    url:    `stage/products/${productId}`,
    method: 'GET'
  })

}

export async function getDraftProductsWidgets() {

  return await fetchy<DraftWidgets>({
    url:    `widgets/staging-products-by-status`,
    method: 'GET',
  })

}

export async function getInventoryWidgets() {
  return await fetchy<InventoryWidgets>({
    url:    `widgets/inventory-status-count`,
    method: 'GET',
  })
}

export async function createProduct( data: CreateProductDetails ) {

  return fetchy<DraftProduct>({
    url:    `stage/products`,
    method: 'POST',
    data
  })

}

export async function bulkUploadProducts( document: FormData ) {

  return await fetchy<BulkProductsResponse>({
    url:     `stage/products/bulk-operations/import`,
    method:  'POST',
    data:    document,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export async function updateDraftProduct( data: DraftProduct, productId: number ) {

  return await fetchy<DraftProduct>({
    url:    `stage/products/${productId}`,
    method: 'PUT',
    data
  })

}

export async function deleteDraftProduct( productId: number ) {

  return await fetchy({
    url:    `stage/products/${productId}`,
    method: 'DELETE',
  })

}

export async function deleteKitComponent( productId: number, kitProductId: number ) {

  return await fetchy({
    url:    `stage/products/${productId}/components/${kitProductId}`,
    method: 'DELETE',
  })

}

export async function createKitComponent( productId: number, data: KitPostData ) {

  return await fetchy({
    url:    `stage/products/${productId}/components`,
    method: 'POST',
    data
  })

}

export async function updateKitComponent( productId: number, kitProductId: number, data: KitPostData ) {

  return await fetchy({
    url:    `stage/products/${productId}/components/${kitProductId}`,
    method: 'PUT',
    data
  })

}

export async function publishDraftProducts( productIds: number[] ) {

  return fetchy({
    url:    `stage/products/bulk-operations/publish`,
    method: 'POST',
    data:   {
      stagingProductIdList: productIds
    }
  })

}
