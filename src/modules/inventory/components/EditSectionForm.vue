<script setup lang="ts">

import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

import type { InputType } from '@lib/components/inputs/input/types'
import type { ProductDetailsSection } from '@/modules/inventory/types'

const props = defineProps<{
  section:      ProductDetailsSection
  savePending?: boolean
}>()

defineEmits<{
  save:  []
  close: []
}>()

function isTextInput( type: InputType | 'toggle' ) {

  return {
    type:   type as InputType,
    isText: [ 'text', 'number', 'email', 'password', 'currency' ].includes( type ),
  }

}

const canSaveChanges = computed(() => props.section.model.length > 0 && props.section.model.filter( field => field.required ).every( field => field.valid ))

</script>

<template>

  <div class="w-full h-full md:min-w-[24rem] grid grid-rows-[max-content_1fr_max-content]">

    <div class="w-full pl-4 flex items-center space-x-3 border-b border-border-subtle-00">

      <Icon
        :name="section.iconName" size="m"
        :class="{
          'text-main': section.key !== 'ERRORS',
          'text-error': section.key === 'ERRORS',
        }"
      />

      <p v-if="section.key === 'ERRORS'" class="text-sm font-medium grow">
        Resolve Errors
      </p>

      <p v-else class="text-sm font-medium grow">
        Edit <span class="text-main">[{{ section.name }}]</span>
      </p>

      <div class="h-full border-l border-border-subtle-00">

        <Button
          type="box"
          mode="ghost"
          icon="close"
          :disabled="savePending"
          @click="$emit('close')"
        />

      </div>

    </div>

    <div
      class="md:p-4 grid content-start overflow-hidden overflow-y-auto"
      :class="{
        'bg-core-30 gap-2': section.key === 'ERRORS',
        'md:gap-2': section.key !== 'ERRORS',
      }"
    >

      <div
        v-for="field, index in section.model.filter(field => !field.nonEditable)"
        :key="field.key"
        :class="{
          'pointer-events-none': savePending,
        }"
      >

        <div v-if="field?.error" class="p-4 bg-core-20">

          <p class="text-sm">
            <span class="text-error">[{{ index + 1 }}]</span> {{ field.error }}
          </p>

        </div>

        <InputField
          v-if="isTextInput(field.type).isText"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          :type="isTextInput(field.type).type"
          :label="field.label"
          :required="field.required"
          :readonly="field.readonly"
          :validation-options="{ min: 0 }"
        >

          <template #suffix>
            <div v-if="field.suffix" class="h-full px-3 grid place-content-center border-l border-core-30">
              <p class="text-sm text-main">
                {{ field.suffix }}
              </p>
            </div>
          </template>

        </InputField>

        <InputField
          v-if="field.type === 'select'"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          type="select"
          :label="field.label"
          :options="field.options"
          :readonly="field.readonly"
        >

          <template #suffix>
            <div v-if="field.suffix" class="h-full px-3 grid place-content-center border-l border-core-30">
              <p class="text-sm text-main">
                {{ field.suffix }}
              </p>
            </div>
          </template>

        </InputField>

        <InputField
          v-if="field.type === 'textarea'"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          type="textarea"
          :label="field.label"
          :required="field.required"
          :readonly="field.readonly"
        />

        <div
          v-if="field.type === 'toggle'"
          class="w-full flex items-center border-b border-b-core-30 md:border-none"
          :class="{
            'bg-core-10 px-4 h-[3.375rem]': section.key === 'ERRORS',
            'px-4 md:px-0 h-10': section.key !== 'ERRORS',
          }"
        >

          <Toggle
            v-model="field.declaredValue"
            v-model:valid="field.valid"
            :readonly="field.readonly"
          >
            <p class="text-sm px-4">
              {{ field.label }}
            </p>

          </Toggle>

        </div>

      </div>

    </div>

    <div class="grid">

      <Button
        :pending="savePending"
        :disabled="!canSaveChanges"
        @click="$emit('save')"
      >
        <p>Save Changes</p>
      </Button>

    </div>

  </div>

</template>
