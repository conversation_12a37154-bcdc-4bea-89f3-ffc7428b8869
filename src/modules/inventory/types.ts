import type { IconName } from '@lib/store/icon'
import type { InputType } from '@lib/components/inputs/input/types'
import type { RouteLocationRaw } from 'vue-router'
import type { ImportError, ImportStatus } from '@/types'

export interface InventoryParams extends BaseParams {
  sku?:           string
  title?:         string
  count?:         number
  sortBy?:        string
  damaged?:       boolean
  inactive?:      boolean
  hasCaseQty?:    boolean
  outOfStock?:    boolean
  inventoryId?:   number
  backOrdered?:   boolean
  supplierName?:  string
  lowInventory?:  boolean
  countOperator?: string
}

export interface DraftProductsParams extends BaseParams {
  sku?:                 string
  supplier?:            string
  isActive?:            boolean
  hasErrors?:           boolean
  productType?:         'KIT' | 'PHYSICAL' | 'VIRTUAL'
  importStatus?:        ImportStatus
  productTitle?:        string
  createdEndDate?:      string
  hasMasterRecord?:     boolean
  createdStartDate?:    string
  defaultFacilityCode?: string
}

export type InventoryViewType = 'draft' | 'live'

export interface BaseKitComponent {
  sku:      string
  quantity: number
}

export interface DraftKitComponent extends BaseKitComponent {
  id:                  number
  importErrors:        ImportError<DraftKitComponent>[]
  importErrorCount:    number
  availableByFacility: AvailableStockLevel[]
}

export interface LiveKitComponent extends BaseKitComponent {
  id: number
}

interface AvailableStockLevelLot {
  lotNumber:   string
  lotQuantity: number
}

export interface AvailableStockLevel {
  lots:           AvailableStockLevelLot[]
  facilityName:   string
  facilityCode:   string
  unitsAvailable: number
}

export interface BaseProduct<ProductType = ''> {
  id:                  number
  sku:                 string
  price:               number
  title:               string
  supplier:            string
  quantity?:           number
  backOrdered?:        number
  kitComponents:       ProductType extends 'live' ? LiveKitComponent[] : ProductType extends 'draft' ? DraftKitComponent[] : BaseKitComponent[]
  availableQuantity:   number
  availableByFacility: AvailableStockLevel[]
}

export interface InventoryPanelProduct extends BaseProduct {
  locked?:      boolean
  quantity?:    number
  inventoryId?: number
}

type SharedBaseProduct = Omit<BaseProduct<'draft'>, 'price' | 'backOrdered' | 'availableQuantity' | 'availableByFacility'>

interface SharedProductProperties {
  upc:                  string
  type:                 string
  isbn:                 string
  size:                 string
  color:                string
  notes:                string
  width:                number
  height:               number
  length:               number
  weight:               number
  webUrl:               string
  isActive:             boolean
  keywords:             string
  imageUrl:             string
  groupCode:            string
  cubicFeet:            number
  retailPrice:          number
  description:          string
  supplierSku:          string
  supplierCost:         number
  customsValue:         number
  caseQuantity:         number
  imageThumbUrl:        string
  customsDescription:   string
  defaultFacilityCode:  string
  packingInstructions:  string
  reorderAlertQuantity: number
}

export interface DraftProduct extends SharedBaseProduct, SharedProductProperties {
  createdTs:          string
  modifiedTs:         string
  importStatus:       ImportStatus
  importErrors:       ImportError<DraftProduct>[]
  masterRecordId:     number
  createdByUserId:    string
  modifiedByUserId:   string
  importErrorCount:   number
  createdByUserName:  string
  masterCaseQuantity: number
  modifiedByUserName: string
}

export interface LiveProduct extends SharedBaseProduct, SharedProductProperties {
  isInsert:             boolean
  trackLots:            boolean
  upsBarcode:           string
  isInternal:           boolean
  isBulkyPick:          boolean
  isBulkyPack:          boolean
  createdDate:          string
  modifiedDate:         string
  scanRequired:         boolean
  isOwdDimensions:      boolean
  isAutoInventory:      boolean
  shipSystemWeight:     boolean
  quantityAvailable:    number
  masterCaseQuantity:   number
  containsDocumentOnly: boolean
  availableStockLevels: AvailableStockLevel[]
}

export interface KitPostData {
  sku:      string
  quantity: number
}

export interface InventoryWidgets {
  outOfStock?:   number
  lowInventory?: number
}

export interface BulkProductsResponse {
  totalRowsInserted: number
  totalRowsRejected: number
}

export enum SectionKeys {
  bulk = 'BULK',
  stock = 'STOCK',
  errors = 'ERRORS',
  customs = 'CUSTOMS',
  supplier = 'SUPPLIER',
  inventory = 'INVENTORY',
  attributes = 'ATTRIBUTES',
  generalInfo = 'GENERAL_INFO',
  kitProducts = 'KIT_PRODUCTS',
  otherDetails = 'OTHER_DETAILS',
  externalLinks = 'EXTERNAL_LINKS',
}

export interface AllProductProperties extends LiveProduct, DraftProduct {}

export interface SectionModel {
  to?:            RouteLocationRaw
  key:            keyof AllProductProperties
  type:           InputType | 'toggle'
  label:          string
  value:          any
  class?:         string
  valid:          boolean
  error?:         string
  suffix?:        string
  hidden?:        boolean
  options?:       DropListOption[]
  readonly?:      boolean
  required?:      boolean
  nonEditable?:   boolean
  externalLink?:  boolean
  declaredValue?: any
}

export interface ProductDetailsSection {
  key:         SectionKeys
  name:        string
  model:       SectionModel[]
  hidden?:     boolean
  iconName:    IconName
  editable?:   boolean
  viewSection: 'left' | 'right'
}
