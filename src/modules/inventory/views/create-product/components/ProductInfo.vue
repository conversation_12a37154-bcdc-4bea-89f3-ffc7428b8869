<script setup lang="ts">

import { appMode } from '@/store'
import { computed, watch } from 'vue'
import { CreateProductMicroFlow } from '@/modules/inventory/views/create-product/types'

import {
  activeStep,
  createNewProduct,
  createProductPending,
  enableAddKitItems,
  isKitProduct,
  kitComponentsValid,
  productDetails,
  productInfoStepValid,
  selectedKitProducts,
} from '@/modules/inventory/views/create-product/store'

import Toggle from '@lib/components/inputs/Toggle.vue'
import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import KitComponent from '@/modules/inventory/views/create-product/components/KitComponent.vue'

const isAdmin = computed(() => appMode === 'ADMIN' )

watch( isKitProduct, ( n ) => {
  if ( !n )
    selectedKitProducts.value = []
})

</script>

<template>

  <div
    class="w-full h-full md:max-h-full grid grid-rows-[1fr_max-content]"
    :class="{
      'md:h-fit': !enableAddKitItems,
      'lg:h-fit': enableAddKitItems,
      'px-0 lg:px-8 pt-0 lg:pt-8 lg:max-w-[38rem]': isKitProduct,
      'md:pt-8 md:px-8 md:pb-0 md:max-w-[38rem]': !isKitProduct,
    }"
  >

    <div class="w-full bg-core-20 shadow-custom">

      <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

        <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
          1
        </div>

        <p class="text-sm font-medium">
          General Information
        </p>

      </div>

      <form
        :class="{
          'pointer-events-none': createProductPending,
        }"
        @submit.prevent
      >

        <div class="px-4 py-2 bg-core-10 text-core-70 text-sm border-b border-core-30 flex">
          <div class="grow">
            Active Product
          </div>
          <Toggle
            v-model="productDetails.isActive.value"
            v-model:valid="productDetails.isActive.valid"
          />
        </div>

        <div
          v-if="isAdmin"
          class="px-4 py-2 bg-core-10 text-core-70 text-sm border-b border-core-30 flex"
        >
          <div class="grow">
            Kit Product
          </div>

          <Toggle v-model="isKitProduct" />

        </div>

        <!-- Kit Product Call To Action -->
        <div
          v-if="isAdmin"
          class="hidden md:block bg-core-30 transition-[height] overflow-hidden text-sm text-core-70 text-center content-center shadow-inner"
          :class="{
            'h-0': !isKitProduct,
            'h-12': isKitProduct,
            'border border-main-40': enableAddKitItems,
          }"
        >

          <div>
            <span class="text-main-70">Add products</span> from your inventory. <span class="text-main-70">*</span>
          </div>

        </div>

        <!-- Kit Product Items -->
        <KitComponent
          v-for="product in selectedKitProducts"
          :key="product.id"
          :kit-component="product"
        />

        <div class="w-full p-0 md:p-7 grid xl:grid-cols-2 md:gap-4">

          <InputField
            v-model="productDetails.sku.value"
            v-model:valid="productDetails.sku.valid"
            class="xl:col-span-2"
            label="SKU"
          />

          <InputField
            v-model="productDetails.title.value"
            v-model:valid="productDetails.title.valid"
            class="xl:col-span-2"
            label="Title"
          />

          <InputField
            v-model="productDetails.reorderAlertQuantity.value"
            v-model:valid="productDetails.reorderAlertQuantity.valid"
            type="number"
            class="xl:col-span-2"
            label="Reorder Alert Quantity"
            :strict="true"
            :validation-options="{ min: 0 }"
          />

          <InputField
            v-model="productDetails.description.value"
            v-model:valid="productDetails.description.valid"
            type="textarea"
            label="Description"
            class="xl:col-span-2"
            :required="false"
          />

        </div>

      </form>

    </div>

    <div
      class="w-full sticky bottom-0 z-2 grid md:hidden bg-core-20"
      :class="{
        'grid-cols-2': !isKitProduct,
      }"
    >

      <Button
        v-if="!isKitProduct"
        mode="secondary"
        :disabled="!(productInfoStepValid && kitComponentsValid)"
        :pending="createProductPending"
        @click="() => createNewProduct((productId) => $router.push({ name: 'Draft Product Details', params: { productId } }))"
      >
        Create Product
      </Button>

      <Button
        :disabled="!productInfoStepValid || createProductPending"
        @click="isKitProduct ? activeStep++ : activeStep = CreateProductMicroFlow.otherDetails"
      >
        Continue
      </Button>

    </div>

  </div>

</template>
