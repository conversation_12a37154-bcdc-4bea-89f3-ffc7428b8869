<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { booleanToYesNo } from '@/store'
import { inventoryParams } from '@/modules/inventory/routes'
import { useRoute, useRouter } from 'vue-router'
import { computed, reactive, ref, watch } from 'vue'
import { bulkExportProducts, getInventoryWidgets, getProducts, inventoryCountOperatorList } from '@/modules/inventory/store'
import { compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, saveFile, viewSetup } from '@lib/scripts/utils'

import Tag from '@lib/components/blocks/tag/Tag.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import CounterWidget from '@/components/CounterWidget.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { SearchFilter } from '@/types'
import type { BaseKitComponent, BaseProduct, InventoryParams, InventoryWidgets } from '@/modules/inventory/types'

interface ProductBooleanParams {
  inactive:     number
  outOfStock:   number
  hasCaseQty:   number
  backOrdered:  number
  lowInventory: number
}

function schema(): TableSchema<BaseProduct> {
  return [
    {
      key:     'id',
      label:   'Product ID',
      sortKey: 'id',
    },
    {
      key:     'sku',
      label:   'SKU',
      sortKey: 'sku',
    },
    {
      key:     'title',
      label:   'Title',
      sortKey: 'title',
    },
    {
      key:     'availableQuantity',
      label:   'Available',
      sortKey: 'available',
    },
    {
      key:     'backOrdered',
      label:   'Back Ordered',
      sortKey: 'backOrdered',
    },
    {
      key:     'price',
      label:   'Price',
      sortKey: 'price',
      format:  'currency',
    },
    {
      key:     'supplier',
      label:   'Supplier',
      sortKey: 'supplier',
    },
  ]
}

function kitSchema(): TableSchema<BaseKitComponent> {
  return [
    {
      key:   'sku',
      label: 'SKU',
    },
    {
      key:   'quantity',
      label: 'Required Kit Quantity',
    }
  ]
}

const { t }       = useI18n()
const total       = ref<number>( 0 )
const route       = useRoute()
const router      = useRouter()
const params      = reactive<InventoryParams>({ ...inventoryParams, ...sanitizeQueryParams( route.query ) })
const pending     = ref<boolean>( false )
const widgets     = ref<InventoryWidgets>( null )
const products    = ref<BaseProduct[]>( [] )
const maxPages    = ref<number>( 0 )
const openFilters = ref<boolean>( false )

// ---- SEARCH PRODUCTS

const searchDefaultModel: InventoryParams = {
  sku:           null,
  title:         null,
  count:         null,
  sortBy:        null,
  damaged:       null,
  inactive:      null,
  hasCaseQty:    null,
  outOfStock:    null,
  inventoryId:   null,
  backOrdered:   null,
  supplierName:  null,
  lowInventory:  null,
  countOperator: null
}

const searchModel = reactive<InventoryParams>({ ...searchDefaultModel, ...sanitizeQueryParams( route.query ) })

const searchBooleansModel = reactive<ProductBooleanParams> ({
  inactive:     null,
  outOfStock:   null,
  hasCaseQty:   null,
  backOrdered:  null,
  lowInventory: null
})

const searchFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const filtersActive = computed(() => searchFilters.value.some( filter => !!filter.value ))

/**
 * Generates the schema for the search filters.
 * @param selectedFilters - The selected filters.
 */

function generateFilters( selectedFilters: Partial<InventoryParams> ): SearchFilter<InventoryParams>[] {
  return [
    {
      key:   'sku',
      label: 'SKU',
      value: selectedFilters?.sku ?? null
    },
    {
      key:   'title',
      label: 'Title',
      value: selectedFilters?.title ?? null
    },
    {
      key:   'inventoryId',
      label: 'Product ID',
      value: selectedFilters?.inventoryId ?? null
    },
    {
      key:   'supplierName',
      label: 'Supplier Name',
      value: selectedFilters?.supplierName ?? null
    },
    {
      key:   'countOperator',
      label: 'Operator',
      value: inventoryCountOperatorList.find( item => item.id === selectedFilters.countOperator )?.name ?? null
    },
    {
      key:   'count',
      label: 'Count',
      value: selectedFilters?.count === 0 ? '0' : selectedFilters?.count ?? null
    },
    {
      key:   'inactive',
      label: 'Is Active',
      value: booleanToYesNo( selectedFilters.inactive, true )
    },
    {
      key:   'hasCaseQty',
      label: 'Has Case QTY',
      value: booleanToYesNo( selectedFilters?.hasCaseQty )
    },
    {
      key:   'backOrdered',
      label: 'Backordered',
      value: booleanToYesNo( selectedFilters?.backOrdered )
    },
    {
      key:   'lowInventory',
      label: 'Low Inventory',
      value: booleanToYesNo( selectedFilters?.lowInventory )
    },
    {
      key:   'outOfStock',
      label: 'Out of Stock',
      value: selectedFilters?.outOfStock ? 'Yes' : null
    }
  ]
}

/**
 * Applies the selected filters to the search model.
 */

function searchInventory() {

  Object.keys( searchModel ).forEach(( key ) => {
    params[key] = searchModel[key]
  })

  openFilters.value = false

}

/**
 * Filters the products by status.
 * @param param - The status to filter by.
 */

function filterByStatus( param: ExtractKeysByType<InventoryParams, boolean> ) {

  resetSelectsOnSearchModel( param as keyof ProductBooleanParams )

  searchModel[param] = true

  Object.keys( searchModel ).forEach(( key ) => {
    params[key] = searchModel[key]
  })

}

/**
 * Resets the filters to their default values.
 */

function resetFilters() {
  Object.keys( searchDefaultModel ).forEach(( key ) => {
    params[key] = searchDefaultModel[key]
    searchModel[key] = searchDefaultModel[key]
    searchBooleansModel[key] = searchDefaultModel[key]
  })
}

/**
 * Resets the search model to its default values.
 */

function resetSearchModel() {
  Object.keys( searchDefaultModel ).forEach(( key ) => {
    searchModel[key] = searchDefaultModel[key]
    searchBooleansModel[key] = searchDefaultModel[key]
  })
}

/**
 * Removes a filter from the search model.
 * @param key - The key of the filter to remove.
 */

function removeFilter( key: keyof InventoryParams | string ) {
  params[key] = null
  searchModel[key] = null
  searchBooleansModel[key] = null
}

/**
 * Resets the selects on the search model.
 * @param param - The parameter to reset.
 */

function resetSelectsOnSearchModel( param: ExtractKeysByType<ProductBooleanParams, number> ) {

  const keysList: ExtractKeysByType<InventoryParams, boolean>[] = [
    'inactive',
    'outOfStock',
    'hasCaseQty',
    'backOrdered',
    'lowInventory'
  ]

  keysList.forEach(( key ) => {
    if ( param !== key ) {
      searchModel[key] = null
      searchBooleansModel[key] = null
    }
  })

}

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams   = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {

      if ( searchModel.hasOwnProperty( key ))
        searchModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchModel[key] = null

    }

    for ( const key in searchBooleansModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchBooleansModel[key] = null

    }

  }

})

// ---- PRODUCT, BATCH & TOOLBAR OPTIONS

function productOptions( record: BaseProduct ): DropListOption[] {
  return [
    {
      id:     1,
      name:   'Details',
      icon:   { name: 'edit' },
      action: () => { router.push({ name: 'Live Product Details', params: { productId: record.id } }) }
    }
  ]
}

function toolbarOptions( hasFilters: boolean ): ToolbarOption[] {
  return [
    {
      id:      'live-products-filters',
      name:    t( 'global.label.search', { name: 'Live Products' }),
      icon:    { name: 'search' },
      compact: false,
      action:  () => { openFilters.value = !openFilters.value },
    },
    {
      id:      'live-products-filters-reset',
      name:    'Reset Filters',
      icon:    { name: 'reset' },
      hidden:  !hasFilters,
      compact: true,
      action:  resetFilters
    }
  ]
}

const productsBatchOptions: BatchOption<BaseProduct>[] = [
  {
    id:     1,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportProducts( selected.map( item => item.id ))

      if ( !error ) {

        const fileName = `products_export_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export Excel'
  },
  {
    id:     2,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportProducts( selected.map( item => item.id ), true )

      if ( !error ) {

        const fileName = `products_export_for_import_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export CSV for import'
  }
]

// ---- GET LIVE PRODUCTS VIEW DATA

/**
 * Maps the children of a product to a nested schema
 * @param records - The records to map
 * @returns The mapped records
 */
function mapChildrenToProducts( records: Tablify<BaseProduct, BaseKitComponent>[] ) {

  if ( !records )
    return null

  records.forEach(( record ) => {

    if ( record.kitComponents ) {

      record.nested = {
        name:         'Kit Components',
        type:         'nested',
        schema:       kitSchema,
        records:      record.kitComponents,
        recordMapKey: 'sku'
      }

    }

  })

  return records

}

async function getLiveProducts( viewParams: InventoryParams ) {

  pending.value = true

  const { error, payload } = await getProducts( viewParams )

  if ( !error ) {
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    products.value = mapChildrenToProducts( payload?.items ) ?? []
  }

  pending.value = false

}

async function getWidgets() {

  const { error, payload } = await getInventoryWidgets()

  if ( !error )
    widgets.value = payload ?? null

}

// ---- VIEW SETUP

viewSetup(
  'Live Products',
  params,
  router,
  [
    { callback: getLiveProducts },
    { callback: getWidgets, ignoreParams: 'all' }
  ]
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr]">

    <div class="w-full p-4 md:px-[1.625rem] grid grid-cols-2 gap-2 md:gap-5 min-[1150px]:gap-8 md:border-none bg-core-10 md:bg-transparent">

      <CounterWidget
        icon="portal-low-inventory"
        title="Low Inventory"
        color="data1-120"
        :count="widgets?.lowInventory"
        :action="() => filterByStatus('lowInventory')"
        :disabled="pending"
      />

      <CounterWidget
        icon="out-of-stock"
        title="Out of Stock"
        color="error"
        :count="widgets?.outOfStock"
        :action="() => filterByStatus('outOfStock')"
        :disabled="pending"
      />

    </div>

    <div class="h-full grid md:px-[1.625rem] md:pb-0 overflow-hidden">

      <Table
        v-model:params="params"
        icon="product"
        name="Live Products"
        class="md:shadow-custom md:border md:border-border-subtle-00"
        record-map-key="id"
        :flex="true"
        :schema="schema"
        :records="products"
        :pending="pending"
        :selectable="true"
        :pagination="{ total, maxPages }"
        resource-name="Product"
        :batch-options="productsBatchOptions"
        :record-options="productOptions"
        :toolbar-options="toolbarOptions(filtersActive)"
        :enable-column-chooser="true"
      >

        <template #table-neck>

          <div v-if="filtersActive" class="p-2 flex flex-wrap items-center gap-1 border-b border-border-subtle-00">

            <Tag
              v-for="filter in searchFilters"
              v-show="filter.value"
              :key="filter.key"
              :label="`${filter.label}: ${filter.value}`"
              @remove="() => removeFilter(filter.key)"
            >
              <p>{{ filter.label }}: <span class="font-medium">{{ filter.value }}</span></p>
            </Tag>

          </div>

        </template>

      </Table>

    </div>

    <!-- Sidebar :: Search Live Products -->

    <Sidebar
      :dim="true"
      :open="openFilters"
      :strict="false"
      @close="openFilters = false"
    >

      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->

        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-layer-01 border-b border-border-subtle-00">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-border-subtle-00">

            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'Live Products' }) }}
            </p>

          </div>

          <div class="h-full border-r border-border-subtle-00">

            <Button
              mode="ghost"
              class="gap-x-2"
              @click="resetSearchModel"
            >

              <p class="text-sm">
                Reset Filters
              </p>

              <Icon name="reset" size="s" class="text-main" />

            </Button>

          </div>

          <Button
            type="box"
            mode="ghost"
            icon="close"
            @click="openFilters = false"
          />

        </div>

        <!-- Search Options -->

        <div class="w-full h-full overflow-hidden overflow-y-auto">

          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>

            <!-- Search By -->

            <section>

              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-layer-01 border-b md:border-b-0 border-border-subtle-00">

                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <InputField
                  v-model="searchModel.sku"
                  label="SKU"
                  :required="false"
                />

                <InputField
                  v-model="searchModel.title"
                  label="Title"
                  :required="false"
                />

                <InputField
                  v-model="searchModel.inventoryId"
                  label="Product ID"
                  :required="false"
                />

                <InputField
                  v-model="searchModel.supplierName"
                  label="Supplier Name"
                  :required="false"
                />

              </div>

            </section>

            <!-- Filter By Availability -->

            <section>

              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-layer-01 border-b md:border-b-0 border-border-subtle-00">

                <p class="text-xs text-core uppercase">
                  Filter By Availability
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <InputField
                  v-model="searchModel.countOperator"
                  type="select"
                  label="Operator"
                  class="hidden md:flex"
                  :options="inventoryCountOperatorList"
                  :required="false"
                />

                <InputField
                  v-model="searchModel.countOperator"
                  type="select"
                  label="Operator"
                  class="md:hidden"
                  :options="inventoryCountOperatorList"
                  :required="false"
                />

                <InputField
                  v-model="searchModel.count"
                  type="number"
                  label="Count"
                  :required="false"
                />

              </div>

            </section>

            <!-- Filter By Status -->

            <section>

              <div class="px-4 md:px-2 h-12 sticky top-0 z-1 flex items-center md:col-span-2 bg-layer-01 border-b md:border-b-0 border-border-subtle-00">

                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.filterByStatus') }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">

                <InputField
                  v-model="searchBooleansModel.inactive"
                  v-model:boolean-model="searchModel.inactive"
                  type="select"
                  label="Is Active"
                  :options="[
                    {
                      id: 1,
                      name: 'Active',
                      mapToBoolean: 'false',
                    },
                    {
                      id: 2,
                      name: 'Inactive',
                      mapToBoolean: 'true',
                    },
                  ]"
                  :required="false"
                  @update:model-value="() => resetSelectsOnSearchModel('inactive')"
                />

                <InputField
                  v-model="searchBooleansModel.hasCaseQty"
                  v-model:boolean-model="searchModel.hasCaseQty"
                  type="select"
                  label="Has Case QTY"
                  :options="[
                    {
                      id: 1,
                      name: 'Yes',
                      mapToBoolean: 'true',
                    },
                  ]"
                  :required="false"
                  @update:model-value="() => resetSelectsOnSearchModel('hasCaseQty')"
                />

                <InputField
                  v-model="searchBooleansModel.backOrdered"
                  v-model:boolean-model="searchModel.backOrdered"
                  type="select"
                  label="Is Backordered"
                  :options="[
                    {
                      id: 1,
                      name: 'Backordered',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not Backordered',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                  @update:model-value="() => resetSelectsOnSearchModel('backOrdered')"
                />

                <InputField
                  v-model="searchBooleansModel.lowInventory"
                  v-model:boolean-model="searchModel.lowInventory"
                  type="select"
                  label="Is Low Inventory"
                  :options="[
                    {
                      id: 1,
                      name: 'Yes',
                      mapToBoolean: 'true',
                    },
                  ]"
                  :required="false"
                  @update:model-value="() => resetSelectsOnSearchModel('lowInventory')"
                />

                <InputField
                  v-model="searchBooleansModel.outOfStock"
                  v-model:boolean-model="searchModel.outOfStock"
                  type="select"
                  label="Is Out Of Stock"
                  class="md:col-span-2"
                  :options="[
                    {
                      id: 1,
                      name: 'Yes',
                      mapToBoolean: 'true',
                    },
                  ]"
                  :required="false"
                  @update:model-value="() => resetSelectsOnSearchModel('outOfStock')"
                />

              </div>

            </section>

          </form>

        </div>

        <!-- Search Buttons -->

        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-layer-01 border-t border-border-subtle-00">

          <Button
            mode="secondary"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>

          <Button
            @click="searchInventory"
          >
            Search
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Sidebar :: Live Product Details -->

    <Sidebar
      :open="['Live Product Details'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView return-to="Live Products" />
      </div>

    </Sidebar>

  </div>

</template>
