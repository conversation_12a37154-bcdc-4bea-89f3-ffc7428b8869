<script  setup lang="ts">

import { Guard } from '@/plugins/guard'
import { useI18n } from 'vue-i18n'
import { asnParams } from '@/modules/asn/routes'
import { useRoute, useRouter } from 'vue-router'
import { computed, reactive, ref, watch } from 'vue'
import { defaultFacility, facilitiesList } from '@/store'
import { AsnStatus, asnStatusList, bulkExportAsns, getAsns, getAsnWidgets } from '@/modules/asn/store'
import { compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, saveFile, viewSetup } from '@lib/scripts/utils'

import Tag from '@lib/components/blocks/tag/Tag.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import CounterWidget from '@/components/CounterWidget.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { SearchFilter } from '@/types'
import type { Asn, AsnParams, AsnWidgets } from '@/modules/asn/types'

const { t }           = useI18n()
const asns            = ref<PaginatedResponse<'asnList', Asn[]>>( )
const total           = ref<number>( 0 )
const route           = useRoute()
const router          = useRouter()
const params          = reactive<AsnParams>({ ...asnParams, ...sanitizeQueryParams( route.name === 'Asns' ? route.query : {}) })
const pending         = ref<boolean>( false )
const maxPages        = ref<number>( 0 )
const openFilters     = ref( false )
const hasFacility     = ref<boolean>( defaultFacility.value !== 'ALL' )
const asnWidgetsData  = ref<AsnWidgets>( null )

function schema( ): TableSchema<Asn> {
  return [
    {
      key:     'id',
      label:   'OWD Reference',
      sortKey: 'id',
    },
    {
      key:     'asnStatus',
      label:   'Status',
      sortKey: 'status',
    },
    {
      key:     'clientReference',
      label:   'Client Reference',
      sortKey: 'clientReference',
    },
    {
      key:     'clientPo',
      label:   'Client PO Number',
      sortKey: 'clientPurchaseOrder',
    },
    {
      key:     'shipperName',
      label:   'Shipper Name',
      sortKey: 'shipperName',
    },
    {
      key:     'expectedDate',
      label:   'Expected Date',
      format:  'date',
      sortKey: 'expectedDate',
    },
    {
      key:     'stockRelease',
      label:   'Stock Release',
      sortKey: 'stockRelease',
    },
    {
      key:     'lastReceivedDate',
      label:   'Last Received',
      format:  'date',
      sortKey: 'lastReceivedDate',
    },
    {
      key:     'pendingReceiveCount',
      label:   'Pending Receipts',
      sortKey: 'pendingReceiveCount',
    },
    {
      key:     'skuCount',
      label:   'SKUs',
      sortKey: 'skuCount',
    }
  ]
}

const searchAsnsDefaultModel: AsnParams = {
  id:              null,
  sku:             null,
  notes:           null,
  sortBy:          null,
  clientPo:        null,
  itemTitle:       null,
  asnStatus:       null,
  shipperName:     null,
  facilityCode:    null,
  expectedDate:    null,
  sortDirection:   null,
  clientReference: null,
}

const searchAsnsModel = reactive({ ...searchAsnsDefaultModel, ...sanitizeQueryParams( route.name === 'Asns' ? route.query : {}) })

function generateFilters( selectedFilters: Partial<AsnParams> ): SearchFilter<AsnParams>[] {
  return [
    {
      key:   'expectedDate',
      label: 'Expected Date',
      value: selectedFilters?.expectedDate ? formatDate( selectedFilters?.expectedDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'facilityCode',
      label: 'Facility',
      value: facilitiesList.value?.find( item => item.id === selectedFilters?.facilityCode )?.name ?? null
    },
    {
      key:   'sku',
      label: 'Product SKU in ASN',
      value: selectedFilters?.sku ?? null
    },
    {
      key:   'asnStatus',
      label: 'Status',
      value: asnStatusList.find( item => item.id === selectedFilters.asnStatus )?.name ?? null
    },
    {
      key:   'itemTitle',
      label: 'Product Title in ASN',
      value: selectedFilters?.itemTitle ?? null
    },
    {
      key:   'shipperName',
      label: 'Shipper Name',
      value: selectedFilters?.shipperName ?? null
    },
    {
      key:   'clientReference',
      label: 'Client Reference',
      value: selectedFilters?.clientReference ?? null
    },
    {
      key:   'clientPo',
      label: 'Client PO Number',
      value: selectedFilters?.clientPo ?? null
    },
    {
      key:   'id',
      label: 'OWD Reference',
      value: selectedFilters?.id ?? null
    },
    {
      key:   'notes',
      label: 'Notes',
      value: selectedFilters?.notes ?? null
    }
  ]
}

const searchAsnsFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const hasFiltersApplied = computed(() => searchAsnsFilters.value.some( filter => !!filter.value ))

function searchAsns() {
  Object.keys( searchAsnsModel ).forEach(( key ) => {
    params[key] = searchAsnsModel[key]
  })

  openFilters.value = false

}

function filterByStatus( status: number ) {
  searchAsnsModel.asnStatus = status
  Object.keys( searchAsnsModel ).forEach(( key ) => {
    params[key] = searchAsnsModel[key]
  })
}

function resetFilters() {

  Object.keys( searchAsnsDefaultModel ).forEach(( key ) => {
    params[key] = searchAsnsDefaultModel[key]
    searchAsnsModel[key] = searchAsnsDefaultModel[key]
  })

}

function resetSearchModel() {
  Object.keys( searchAsnsDefaultModel ).forEach(( key ) => {
    searchAsnsModel[key] = searchAsnsDefaultModel[key]
  })
}

function removeFilter( key: string ) {
  params[key] = null
  searchAsnsModel[key] = null
}

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams   = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {

      if ( searchAsnsModel.hasOwnProperty( key ))
        searchAsnsModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchAsnsModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchAsnsModel[key] = null

    }

  }

})

function asnRowOptions( record: Asn ): DropListOption[] {
  return [
    {
      id:   1,
      name: 'Details',
      icon: {
        name: 'edit',
        size: 'm'
      },
      action: () => {
        router.push({ name: 'Asn Details', params: { asnId: record.id } })
      }
    }
  ]
}

function toolbarOptions( hasFilters: boolean ): ToolbarOption[] {
  return [
    {
      id:      'asn-filters',
      name:    t( 'global.label.search', { name: 'ASNs' }),
      icon:    { name: 'search' },
      compact: false,
      action:  () => { openFilters.value = !openFilters.value },
    },
    {
      id:      'asn-filters-reset',
      name:    'Reset Filters',
      icon:    { name: 'reset' },
      hidden:  !hasFilters,
      compact: true,
      action:  resetFilters
    }
  ]
}

const asnsBatchOptions: BatchOption<Asn>[] = [
  {
    id:     1,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportAsns( selected.map( item => item.id ))

      if ( !error ) {

        const fileName = `asn_export_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export Excel'
  }
]

async function getAsnWidgetsData() {

  const response = await getAsnWidgets()

  asnWidgetsData.value = response.payload

}

async function getAsnList( viewParams: AsnParams ) {

  pending.value = true

  const { payload } = await getAsns( viewParams )

  asns.value = payload ?? null
  total.value = payload?.totalRows ?? 0
  maxPages.value = payload?.totalPages ?? 0

  pending.value = false

}

function openCreateAsn() {
  router.push({ name: 'Create Asn' })
}

function closeCreateAsn() {
  blockPageUpdate()
  router.push({ name: 'Asns' })
}

const { blockPageUpdate } = viewSetup(
  'Asns',
  params,
  router,
  [
    { callback: getAsnList },
    { callback: getAsnWidgetsData, ignoreParams: 'all' }
  ]
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <div class="w-full pl-4 h-9 flex items-center border-b border-border-subtle-00 bg-layer-01">

      <h3 class="text-sm font-medium">
        Your ASNs
      </h3>

      <div class="grow" />

      <Guard scope="Asn.Write">

        <div class="h-full border-l border-border-subtle-00">

          <Button
            size="auto"
            mode="ghost"
            class="h-full gap-x-2"
            @click="openCreateAsn()"
          >

            <p class="text-sm font-medium">
              New ASN
            </p>

            <Icon name="add" size="s" />

          </Button>

        </div>

      </Guard>

    </div>

    <div class="grid grid-rows-[max-content_1fr] overflow-hidden">

      <div class="w-full p-2 sm:p-4 md:px-[1.625rem] grid grid-cols-4 gap-2 xl:gap-4 sm:grid-cols-2 xl:grid-cols-4 bg-layer-01 md:bg-transparent">

        <CounterWidget :disabled="pending" title="Pending" :count="asnWidgetsData?.pending" icon="pending" :action="() => filterByStatus(AsnStatus.PENDING)" color="data1-120" />
        <CounterWidget :disabled="pending" title="Received" :count="asnWidgetsData?.received" icon="received" :action="() => filterByStatus(AsnStatus.RECEIVED)" color="success" />
        <CounterWidget :disabled="pending" title="Partial Receipt" :count="asnWidgetsData?.partialReceipt" icon="in-progress" :action="() => filterByStatus(AsnStatus.PARTIAL_RECEIPT)" color="main" />
        <CounterWidget :disabled="pending" title="Canceled" :count="asnWidgetsData?.cancelled" icon="canceled" :action="() => filterByStatus(AsnStatus.CANCELED)" color="error" />

      </div>

      <div class="h-full grid md:px-[1.625rem] md:pb-0 overflow-hidden">

        <Table
          v-model:params="params"
          name="ASNs"
          icon="portal-asn"
          class="md:border md:border-border-subtle-00"
          :flex="true"
          :schema="schema"
          :records="asns?.asnList || []"
          :pending="pending"
          record-map-key="id"
          :selectable="true"
          resource-name="ASN"
          :batch-options="asnsBatchOptions"
          :record-options="asnRowOptions"
          :toolbar-options="toolbarOptions(hasFiltersApplied)"
          :enable-column-chooser="true"
          :pagination="{
            total,
            maxPages,
          }"
        >

          <template #table-neck>

            <div v-if="hasFiltersApplied" class="p-2 flex flex-wrap items-center gap-1 border-b border-border-subtle-00">

              <Tag
                v-for="filter in searchAsnsFilters"
                v-show="filter.value"
                :key="filter.key"
                :label="`${filter.label}: ${filter.value}`"
                @remove="() => removeFilter(filter.key)"
              >
                <p>{{ filter.label }}: <span class="font-medium">{{ filter.value }}</span></p>
              </Tag>

            </div>

          </template>

        </Table>

      </div>

    </div>

    <Sidebar
      :open="openFilters"
      :strict="false"
      :dim="true"
      @close="openFilters = false"
    >
      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->

        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-layer-01 border-b border-border-subtle-00">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-border-subtle-00">

            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'ASNs' }) }}
            </p>

          </div>

          <div class="h-full border-r border-border-subtle-00">

            <Button
              mode="ghost"
              class="gap-x-2"
              @click="resetSearchModel"
            >
              <p class="text-sm">
                Reset Filters
              </p>
              <Icon name="reset" size="s" class="text-main" />
            </Button>

          </div>

          <Button
            type="box"
            mode="ghost"
            icon="close"
            @click="openFilters = false"
          />

        </div>

        <!-- Search Options -->
        <div class="w-full h-full overflow-hidden overflow-y-auto">

          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>

            <!-- Search By -->

            <section>

              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-layer-01 border-b md:border-b-0 border-border-subtle-00">

                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">

                <InputField
                  v-show="!hasFacility"
                  v-model="searchAsnsModel.facilityCode"
                  type="select"
                  label="Facility"
                  class="hidden md:block col-span-2"
                  :options="facilitiesList"
                  :disabled="hasFacility"
                  :required="false"
                />

                <InputField
                  v-show="!hasFacility"
                  v-model="searchAsnsModel.facilityCode"
                  type="select"
                  label="Facility"
                  class="md:hidden"
                  :options="facilitiesList"
                  :required="false"
                  :disabled="hasFacility"
                />

                <InputField
                  v-model="searchAsnsModel.asnStatus"
                  type="select"
                  label="Status"
                  class="hidden md:block"
                  :options="asnStatusList"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.asnStatus"
                  type="select"
                  label="Status"
                  class="md:hidden"
                  :options="asnStatusList"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.sku"
                  label="Product SKU in ASN"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.itemTitle"
                  label="Product Title in ASN"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.shipperName"
                  label="Shipper Name"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.clientReference"
                  label="Client Reference"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.clientPo"
                  label="Client PO Number"
                  :required="false"
                />

                <InputField
                  v-model="searchAsnsModel.id"
                  label="OWD Reference"
                  :required="false"
                />

                <DatePicker
                  v-model="searchAsnsModel.expectedDate"
                  label="Expected Date"
                  :required="false"
                  return-type="UTC-date-time"
                />

                <InputField
                  v-model="searchAsnsModel.notes"
                  type="textarea"
                  label="Notes"
                  class="md:col-span-2"
                  :required="false"
                />
              </div>

            </section>

          </form>

        </div>

        <!-- Search Buttons -->

        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-layer-01 border-t border-border-subtle-00">

          <Button
            mode="secondary"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>

          <Button @click="searchAsns">
            Search
          </Button>

        </div>

      </div>

    </Sidebar>

    <Sidebar
      :open="['Asn Details', 'Asn Items Inventory'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView />
      </div>

    </Sidebar>

    <Sidebar
      :open="['Create Asn', 'Create Asn Inventory'].includes(String($route.name))"
      :strict="true"
      :fit-content="false"
      @close="closeCreateAsn"
    >

      <div class="w-full h-full">
        <RouterView @close="closeCreateAsn" />
      </div>

    </Sidebar>

  </div>

</template>
