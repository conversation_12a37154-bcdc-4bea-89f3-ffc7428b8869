<script setup lang="ts">

import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { onMounted, ref } from 'vue'
import { asnCarriersList } from '@/modules/asn/store'
import { defaultFacility, facilitiesList } from '@/store'
import { activeStep, asnGeneralDetailsStepValid, asnGeneralInfo } from '@/modules/asn/views/CreateAsn/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Radio from '@lib/components/inputs/Radio.vue'
import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import OptionsGroup from '@lib/components/blocks/OptionsGroup.vue'

const hasFacility = ref<boolean>( false )

function goToStepTwo() {

  activeStep.value = Flow.PRODUCTS

}

onMounted(() => {

  asnGeneralInfo.value.facilityCode.value = defaultFacility.value === 'ALL'
    ? asnGeneralInfo.value.facilityCode.value
    : defaultFacility.value

  hasFacility.value = defaultFacility.value !== 'ALL'

})

defineExpose({
  hasFacility
})

</script>

<template>

  <div class="w-full h-full flex flex-col justify-between md:justify-normal md:grid md:grid-rows-[1fr_max-content] md:overflow-hidden overflow-y-auto bg-core-20 md:bg-transparent">

    <div class="w-full md:h-full lg:p-8 lg:pb-0 md:grid md:grid-cols-2 lg:gap-8 md:overflow-hidden">

      <div data-section="client" class="w-full h-fit md:h-full lg:h-fit lg:max-h-full grid grid-rows-[max-content_max-content] md:overflow-y-auto lg:shadow-custom border-r border-core-30 lg:border-r-0 bg-core-20 lg:bg-transparent">

        <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

          <Icon name="details" size="m" class="text-main" />

          <p class="text-sm font-semibold">
            Client Details
          </p>

        </div>

        <form class="h-fit w-full bg-core-20 p-0 md:px-8 md:py-9 grid md:grid-cols-1 lg:grid-cols-2 md:gap-4" @submit.prevent>
          <InputField
            v-model:valid="asnGeneralInfo.clientReference.valid"
            v-model="asnGeneralInfo.clientReference.value"
            label="Client Reference"
            :required="false"
            class="lg:col-span-1"
          />
          <InputField
            v-model="asnGeneralInfo.clientPo.value"
            v-model:valid="asnGeneralInfo.clientPo.valid"
            label="Client PO Number"
            class="lg:col-span-1"
            :required="false"
          />
          <InputField
            v-model="asnGeneralInfo.notes.value"
            v-model:valid="asnGeneralInfo.notes.valid"
            type="textarea"
            class="lg:col-span-2"
            label="Instruction Notes"
            :required="false"
          />
        </form>

      </div>

      <div data-section="shipping" class="w-full h-fit md:h-full lg:h-fit lg:max-h-full grid grid-rows-[1fr] md:overflow-y-auto lg:shadow-custom border-r border-core-30 lg:border-r-0">

        <div class="w-full bg-core-20">

          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

            <Icon name="shipped" size="m" class="text-main" />

            <p class="text-sm font-semibold">
              Shipping Details
            </p>

          </div>

          <form class="w-full p-0 md:px-8 md:py-9 grid lg:grid-cols-2 md:gap-4" @submit.prevent>
            <InputField
              v-model="asnGeneralInfo.shipperName.value"
              v-model:valid="asnGeneralInfo.shipperName.valid"
              label="Shipper Name"
              :required="true"
            />

            <DatePicker
              v-model="asnGeneralInfo.expectedDate.value"
              v-model:valid="asnGeneralInfo.expectedDate.valid"
              label="Expected Date"
              :required="true"
              :limit-from="new Date().toISOString().split('T')[0]"
            />

            <InputField
              v-model="asnGeneralInfo.carrier.value"
              v-model:valid="asnGeneralInfo.carrier.valid"
              type="select"
              label="Carrier"
              :required="true"
              :nullable="false"
              :options="asnCarriersList"
              class="lg:col-span-2"
            />

            <InputField
              v-model="asnGeneralInfo.cartonCount.value"
              v-model:valid="asnGeneralInfo.cartonCount.valid"
              type="number"
              label="Carton/Package Count"
              :strict="true"
              :required="false"
              :validation-options="{ min: 0 }"
            />

            <InputField
              v-model="asnGeneralInfo.palletCount.value"
              v-model:valid="asnGeneralInfo.palletCount.valid"
              type="number"
              label="Pallet Count"
              :strict="true"
              :required="false"
              :validation-options="{ min: 0 }"
            />

            <Radio
              v-show="!hasFacility"
              v-model="asnGeneralInfo.facilityCode.value"
              v-model:valid="asnGeneralInfo.facilityCode.valid"
              :options="facilitiesList"
              :required="true"
              label="Shipment Facility"
              :disabled="hasFacility"
            />

            <OptionsGroup
              label="Auto Release"
              class="pt-4"
            >
              <Checkbox v-model="asnGeneralInfo.isAutoRelease.value" v-model:valid="asnGeneralInfo.isAutoRelease.valid">

                <span>Auto Release</span>

                <template #suffix>
                  <Icon
                    v-tooltip="{ content: 'Check the Auto Release to make your inventory immediately available for orders as it is received and counted. Uncheck the box above if you want items in this shipment to not be added to available inventory until after review by you or your Account Manager following receipt.' }"
                    name="info"
                    size="m"
                    class="text-main ml-2"
                  />
                </template>

              </Checkbox>
            </OptionsGroup>

          </form>

        </div>

      </div>

    </div>

    <div class="w-full h-12 sticky bottom-0 z-1 flex items-center justify-end bg-core-20 border-t border-core-30">

      <Button
        data-button="continue"
        :disabled="!asnGeneralDetailsStepValid"
        class="w-full md:px-12 md:w-auto"
        size="l"
        @click="goToStepTwo"
      >
        Continue
      </Button>

    </div>

  </div>

</template>
