<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref } from 'vue'
import { defaultFacility, facilitiesList } from '@/store'
import { asn, asnCarriersList, updateAsnDetails } from '@/modules/asn/store'
import { checkValue, convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'

import type { AsnDetails } from '@/modules/asn/types'

const emits = defineEmits<{ close: [] }>()

const asnDetailsInfo: AsnDetails = {
  notes:           asn.value?.notes || null,
  carrier:         asn.value?.carrier || null,
  facility:        asn.value?.facility || null,
  clientPo:        asn.value?.clientPo || null,
  asnStatus:       asn.value?.asnStatus || null,
  palletCount:     checkValue( asn.value?.cartonCount ) ? asn.value.palletCount : null,
  cartonCount:     checkValue( asn.value?.cartonCount ) ? asn.value.cartonCount : null,
  shipperName:     asn.value?.shipperName || null,
  expectedDate:    asn.value?.expectedDate || null,
  facilityCode:    asn.value?.facilityCode || null,
  isAutoRelease:   asn.value?.isAutoRelease || null,
  clientReference: asn.value?.clientReference || null,
}

const pending       = ref<boolean>( false )
const canCreate     = computed(() => validateModel( asnFormModel ))
const asnFormModel  = reactive<Validatable<AsnDetails>>( convertObjectToValidatable( asnDetailsInfo, null, [ 'facility', 'clientReference', 'clientPo', 'isAutoRelease', 'asnStatus' ], ))

async function submitAsn() {

  pending.value = true

  const asnFormData = convertObjectToPlain( asnFormModel )

  const { payload } = await updateAsnDetails(
    asn.value.id,
    {
      ...asnFormData,
      palletCount: checkValue( asnFormData.palletCount ) ? asnFormData.palletCount : null,
      cartonCount: checkValue( asnFormData.cartonCount ) ? asnFormData.cartonCount : null,
    }
  )

  if ( payload ) {

    pending.value = true

    asn.value = payload

    setNotificationOptions({ message: 'ASN details are updated successfully.' })

  }

  emits( 'close' )

}

defineExpose({
  asnFormModel,
  submitAsn,
  canCreate
})

</script>

<template>

  <div class="md:w-[38rem] h-full grid grid-rows-[max-content_1fr]">

    <div class="h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

      <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

        <Icon name="edit" size="m" class="text-main" />

        <p data-element="header" class="text-sm font-medium">
          Edit <span class="text-main">[{{ asn?.id }}]</span>
        </p>

      </div>

      <Button
        type="box"
        mode="ghost"
        icon="close"
        :disabled="pending"
        @click="emits('close')"
      />

    </div>

    <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">
      <form
        class="md:p-8 h-full flex flex-col md:grid md:grid-cols-2 md:gap-4 content-start overflow-y-auto"
        :class="{
          'pointer-events-none': pending,
        }"
        @submit.prevent
      >
        <InputField
          v-model="asnFormModel.shipperName.value"
          v-model:valid="asnFormModel.shipperName.valid"
          label="Shipper Name"
          :required="true"
        />
        <InputField
          v-model="asnFormModel.facilityCode.value"
          v-model:valid="asnFormModel.facilityCode.valid"
          type="select"
          label="Facility"
          :options="facilitiesList"
          :required="true"
          :nullable="false"
          :readonly="defaultFacility !== 'ALL'"
        />
        <DatePicker
          v-model="asnFormModel.expectedDate.value"
          v-model:valid="asnFormModel.expectedDate.valid"
          label="Expected Date"
          :required="true"
          size="l"
          :limit-from="new Date().toISOString().split('T')[0]"
        />
        <InputField
          v-model="asnFormModel.carrier.value"
          v-model:valid="asnFormModel.carrier.valid"
          type="select"
          label="Carrier"
          :options="asnCarriersList"
          :required="true"
          :nullable="false"
        />
        <InputField
          v-model:valid="asnFormModel.clientReference.valid"
          v-model="asnFormModel.clientReference.value"
          label="Client Reference"
          :required="false"
        />
        <InputField
          v-model="asnFormModel.clientPo.value"
          v-model:valid="asnFormModel.clientPo.valid"
          label="Client PO Number"
          :required="false"
        />
        <InputField
          v-model="asnFormModel.cartonCount.value"
          v-model:valid="asnFormModel.cartonCount.valid"
          type="number"
          label="Carton/Package Count"
          :strict="true"
          :required="false"
          :validation-options="{ min: 0 }"
        />
        <InputField
          v-model="asnFormModel.palletCount.value"
          v-model:valid="asnFormModel.palletCount.valid"
          type="number"
          label="Pallet Count"
          :strict="true"
          :required="false"
          :validation-options="{ min: 0 }"
        />
        <InputField
          v-model="asnFormModel.notes.value"
          v-model:valid="asnFormModel.notes.valid"
          type="textarea"
          label="Instruction Notes"
          class="col-span-2"
          :required="false"
        />
        <div class="p-4 md:p-0 flex flex-col gap-y-2">
          <p class="text-xs text-core uppercase">
            Receiving options
          </p>

          <Checkbox
            v-model="asnFormModel.isAutoRelease.value"
            v-model:valid="asnFormModel.isAutoRelease.valid"
            class="col-span-2"
          >

            <span>Auto Release</span>

            <template #suffix>
              <Icon
                v-tooltip="{ content: 'Check the Auto Release to make your inventory immediately available for orders as it is received and counted. Uncheck the box above if you want items in this shipment to not be added to available inventory until after review by you or your Account Manager following receipt.' }"
                name="info"
                size="m"
                class="text-main  ml-2"
              />
            </template>

          </Checkbox>

        </div>
      </form>
      <div class="w-full h-12 sticky top-0 z-1 flex bg-core-20 border-t border-core-30">

        <Button
          class="w-full"
          :pending="pending"
          :disabled="!canCreate"
          data-button="save-changes"
          @click="submitAsn"
        >
          Save Changes
        </Button>

      </div>
    </div>

  </div>

</template>
