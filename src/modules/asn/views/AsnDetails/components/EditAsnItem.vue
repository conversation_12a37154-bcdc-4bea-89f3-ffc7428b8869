<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { asn, updateAsnItem } from '@/modules/asn/store'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref } from 'vue'
import { convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

import type { AsnItem } from '@/modules/asn/types'

const props = defineProps<{ asnItemsData: AsnItem[], lineItemId: number }>()

const emits = defineEmits<{
  ( event: 'close' ): void
}>()

const asnItems = defineModel<AsnItem[]>( 'asnItemsData', { default: [] })
const item     = computed(() => asnItems.value?.find( item => item.id === props.lineItemId ))

const editItemModel: AsnItem = {
  id:            item.value?.id,
  sku:           item.value?.sku,
  title:         item.value?.title,
  notes:         item.value?.notes,
  description:   item.value?.description,
  expectedUnits: item.value?.expectedUnits
}

const asnId         = asn.value.id
const itemModel     = reactive<Validatable<AsnItem>>( convertObjectToValidatable( editItemModel, null, [ 'sku', 'title', 'description', 'id', 'notes' ] ))
const canUpdate     = computed(() => validateModel( itemModel ))
const updatePending = ref<boolean>( false )
const deletePending = ref<boolean>( false )

async function updateItem() {

  updatePending.value = true

  const itemToUpdate = convertObjectToPlain( itemModel )

  const { payload } = await updateAsnItem( asnId, { ...itemToUpdate })

  if ( payload ) {

    setNotificationOptions({ message: 'ASN product is updated successfully.' })

    asnItems.value.forEach(( asnItem ) => {

      if ( asnItem.inventoryId === payload.inventoryId ) {

        asnItem.title = payload.title
        asnItem.notes = payload.notes
        asnItem.description = payload.description
        asnItem.expectedUnits = payload.expectedUnits

      }

    })

    emits( 'close' )
  }

}
defineExpose({ updateItem, updatePending, canUpdate, itemModel })

</script>

<template>

  <div class="h-full w-full md:w-[26.75rem] relative grid grid-rows-[max-content_1fr_max-content]">

    <div class="w-full h-12 flex items-center border-b border-core-30">

      <div class="h-full px-4 flex items-center grow">

        <p class="text-sm font-medium">
          Edit Product: <span class="text-main truncate">[{{ itemModel.sku.value }}]</span>
        </p>

      </div>

      <Button
        size="l"
        type="box"
        mode="ghost"
        icon="close"
        data-button="close"
        :disabled="updatePending"
        @click="emits('close')"
      />

    </div>

    <div class="overflow-y-auto">
      <form
        class="md:p-4 grid md:gap-4"
        :class="{
          'pointer-events-none': updatePending,
        }"
        @submit.prevent
      >
        <InputField
          v-model="itemModel.sku.value"
          v-model:valid="itemModel.sku.valid"
          label="SKU"
          :readonly="true"
          :required="true"
        />
        <InputField
          v-model="itemModel.title.value"
          v-model:valid="itemModel.title.valid"
          label="Title"
          :required="true"
        />
        <InputField
          v-model="itemModel.expectedUnits.value"
          v-model:valid="itemModel.expectedUnits.valid"
          type="number"
          label="Unit"
          :strict="true"
          :required="true"
          :validation-options="{ min: 1 }"
        />
        <InputField
          v-model="itemModel.description.value"
          type="textarea"
          label="Description"
          :required="false"
        />
        <InputField
          v-model="itemModel.notes.value"
          type="textarea"
          label="Notes"
          :required="false"
        />
      </form>
    </div>

    <div class="w-full h-12 sticky top-0 z-1 grid bg-core-20 border-t border-core-30">

      <Button
        :pending="updatePending"
        :disabled="!canUpdate || deletePending"
        data-button="update"
        @click="updateItem"
      >
        Save Product
      </Button>

    </div>

  </div>

</template>
