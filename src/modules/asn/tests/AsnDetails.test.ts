import { ref } from 'vue'
import { asn } from '@/modules/asn/store'
import { formatDate } from '@lib/scripts/utils'
import { mount, shallowMount } from '@vue/test-utils'
import { booleanToYesNo, defaultFacility } from '@/store'
import { beforeEach, describe, expect, it, vi } from 'vitest'

import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import AsnInfo from '@/modules/asn/views/AsnDetails/components/AsnInfo.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import AsnDetails from '@/modules/asn/views/AsnDetails/AsnDetails.vue'
import DetailsCard from '@/components/DetailsCard.vue'
import AsnDetailsForm from '@/modules/asn/views/AsnDetails/components/AsnDetailsForm.vue'

import type { AsnDetails as AsnDetailsType } from '@/modules/asn/types'

const mockAsn: AsnDetailsType = {
  id:              159608,
  clientReference: 'brembo-asn',
  clientPo:        'brembo-po',
  asnStatus:       'Pending',
  shipperName:     'brembo-inc',
  cartonCount:     12,
  palletCount:     128,
  notes:           '',
  facilityCode:    'DC1',
  facility:        'Midwest - Mobridge, SD',
  carrier:         'UPS',
  expectedDate:    new Date( '2024-09-30T15:52:10+02:00' ),
  createdDate:     new Date( '2024-09-20T10:51:45.86+02:00' ),
  createdBy:       'New Portal User',
  hasBlind:        false,
  isAutoRelease:   true,
  isActive:        true
}

const mockAsnItems = {
  asnItems: [
    {
      id:            1459325,
      inventoryId:   812563,
      sku:           'BLACK-CAR-paint-v2',
      description:   '',
      title:         'Black Car paint v2',
      notes:         '',
      expectedUnits: 1,
      receivedUnits: 0,
      itemReceives:  null
    },
    {
      id:            1459324,
      inventoryId:   812564,
      sku:           'BLACK-CAR-paint-v3',
      description:   '',
      title:         'Black Car paint v3',
      notes:         '',
      expectedUnits: 1,
      receivedUnits: 0,
      itemReceives:  null
    },
    {
      id:            1459326,
      inventoryId:   403441,
      sku:           'chead-dl-combo',
      description:   '',
      title:         'Choppahead Download Bundle!  (Watch Vols 1, 2, 3, 4) - NEW!!',
      notes:         '',
      expectedUnits: 1,
      receivedUnits: 0,
      itemReceives:  null
    },
    {
      id:            1459327,
      inventoryId:   360455,
      sku:           'dl-vcs-1',
      description:   '',
      title:         'VCS Vol 1 (full movie download)',
      notes:         '',
      expectedUnits: 1,
      receivedUnits: 0,
      itemReceives:  null
    }
  ],
  currentPage: 1,
  pageSize:    25,
  nextPage:    1,
  totalRows:   4,
  totalPages:  1,
  isLastPage:  true
}

const mocks = vi.hoisted(() => ({
  guard: vi.fn(() => true ),
  asn:   vi.fn((): AsnDetailsType => ({
    id:              159608,
    clientReference: 'brembo-asn',
    clientPo:        'brembo-po',
    asnStatus:       'Pending',
    shipperName:     'brembo-inc',
    cartonCount:     12,
    palletCount:     128,
    notes:           '',
    facilityCode:    'DC1',
    facility:        'Midwest - Mobridge, SD',
    carrier:         'UPS',
    expectedDate:    new Date( '2024-09-30T15:52:10+02:00' ),
    createdDate:     new Date( '2024-09-20T10:51:45.86+02:00' ),
    createdBy:       'New Portal User',
    hasBlind:        false,
    isAutoRelease:   true,
    isActive:        true,
  }))
}))

const mockRoute = ref({
  name:     'Asn Details',
  path:     '/asn/details/159608?someParam=someValue',
  params:   { asnId: '159608' },
  fullPath: '/asn/details/159608',
  query:    {},
})

vi.mock( 'vue-router', () => ({
  useRoute:  vi.fn(() => mockRoute.value ),
  useRouter: vi.fn(() => ({
    currentRoute: { value: mockRoute.value },
    push:         vi.fn(),
  })),
}))

vi.mock( '@/modules/asn/store', async ( importOriginal ) => {
  const actual = await importOriginal() as any

  return {
    ...actual,
    getAsn: vi.fn(() => {
      return new Promise(( resolve ) => {
        setTimeout(() => {
          resolve({
            error:   null,
            payload: mocks.asn(),
          })
        }, 500 )
      })
    }),
    getAsnItems: vi.fn(() => {
      return new Promise(( resolve ) => {
        setTimeout(() => {
          resolve({
            error:   null,
            payload: mockAsnItems,
          })
        }, 500 )
      })
    })
  }
})

vi.mock( '@/store', async ( importOriginal ) => {
  const actual = await importOriginal() as any

  actual.facilitiesList.value = [
    {
      id:   'DC1',
      name: 'Midwest - Mobridge, SD'
    },
    {
      id:   'DC6',
      name: 'West - Los Angeles, CA'
    },
    {
      id:   'DC7',
      name: 'East - Wilmington, OH'
    }
  ]

  return {
    ...actual,
  }

})

describe( 'asn details - [ view ]', async () => {

  // Create a div to serve as the teleport target
  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const wrapper = mount( AsnDetails, {
    attachTo: document.body,
  })

  it( 'hides the desktop action sidebar if openDesktopActions is false', async () => {

    await wrapper.vm.$nextTick()

    const sidebars = wrapper.findAllComponents( Sidebar )

    const desktopSidebar = sidebars.find( sidebar => sidebar.props().name === 'desktop-actions' )

    expect( desktopSidebar.props().open ).toBe( false )

  })

  it( 'shows the desktop action sidebar if openDesktopActions is true', async () => {

    wrapper.vm.openDesktopActions = true

    await wrapper.vm.$nextTick()

    const sidebars = wrapper.findAllComponents( Sidebar )

    const desktopSidebar = sidebars.find( sidebar => sidebar.props().name === 'desktop-actions' )
    expect( desktopSidebar.props().open ).toBe( true )

  })

  it( 'hides the mobile action sidebar if openMobileActions is false', async () => {

    wrapper.vm.openMobileActions = false

    await wrapper.vm.$nextTick()

    const sidebars = wrapper.findAllComponents( Sidebar )

    const desktopSidebar = sidebars.find( sidebar => sidebar.props().name === 'mobile-actions' )

    expect( desktopSidebar.props().open ).toBe( false )

  })

  it( 'shows the mobile action sidebar if openMobileActions is true', async () => {

    wrapper.vm.openMobileActions = true

    await wrapper.vm.$nextTick()

    const sidebars = wrapper.findAllComponents( Sidebar )

    const desktopSidebar = sidebars.find( sidebar => sidebar.props().name === 'mobile-actions' )

    expect( desktopSidebar.props().open ).toBe( true )

  })

  describe( 'asn status is Partial Receipt', () => {

    it( 'shows the Mark ASN as Received button if ASN is Partial Receipt', async () => {

      mocks.asn.mockReturnValue({ ...mockAsn, asnStatus: 'Partial Receipt' })

      expect( mocks.asn().asnStatus ).toBe( 'Partial Receipt' )

      await wrapper.vm.getAsnDetails( )

      wrapper.vm.openDesktopActions = true

      const allButtons = wrapper.findAllComponents( Button )
      const actionButtons = allButtons.filter( b => b.attributes()['data-button-desktop'] === 'actions' )
      const markAsReceivedButton = actionButtons.find( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionButtons.length ).toBe( 1 )
      expect( markAsReceivedButton.text()).toBe( 'Mark ASN as Received' )

    })

    it( 'sets the canEdit value to false when the status is Partial Receipt', () => {
      expect( wrapper.vm.canEdit ).toBe( false )
    })

    it( 'sets the isPartial value to true when the status is Partial Receipt', () => {
      expect( wrapper.vm.isPartial ).toBe( true )
    })

  })

  describe( 'asn status is Pending', () => {

    it( 'shows only the Deactivate ASN button if ASN is Pending', async () => {

      mocks.asn.mockReturnValue({ ...mockAsn, asnStatus: 'Pending' })

      expect( mocks.asn().asnStatus ).toBe( 'Pending' )

      await wrapper.vm.getAsnDetails( )

      wrapper.vm.openDesktopActions = true

      const allButtons = wrapper.findAllComponents( Button )
      const actionButtons = allButtons.filter( b => b.attributes()['data-button-desktop'] === 'actions' )
      const deactivateAsn = actionButtons.find( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionButtons.length ).toBe( 1 )
      expect( deactivateAsn.text()).toBe( 'Cancel ASN' )

    })

    it( 'sets the canEdit value to true when the status is Pending', () => {
      expect( wrapper.vm.canEdit ).toBe( true )
    })

    it( 'sets the isPartial value to false when the status is Pending', () => {
      expect( wrapper.vm.isPartial ).toBe( false )
    })

  })

  describe( 'asn status is Canceled', () => {

    it( 'hides all action buttons if ASN is Canceled', async () => {

      mocks.asn.mockReturnValue({ ...mockAsn, asnStatus: 'Canceled' })

      expect( mocks.asn().asnStatus ).toBe( 'Canceled' )

      await wrapper.vm.getAsnDetails( )

      wrapper.vm.openDesktopActions = true

      const allButtons = wrapper.findAllComponents( Button )
      const allActionButtons = allButtons.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( allActionButtons.length ).toBe( 0 )

    })

    it( 'sets the canEdit value to false when the status is Canceled', () => {
      expect( wrapper.vm.canEdit ).toBe( false )
    })

    it( 'sets the isPartial value to false when the status is Canceled', () => {
      expect( wrapper.vm.isPartial ).toBe( false )
    })

  })

  describe( 'asn status is Received', () => {

    it( 'hides all action buttons if ASN is Received', async () => {

      mocks.asn.mockReturnValue({ ...mockAsn, asnStatus: 'Received' })

      expect( mocks.asn().asnStatus ).toBe( 'Received' )

      await wrapper.vm.getAsnDetails( )

      wrapper.vm.openDesktopActions = true

      const allButtons = wrapper.findAllComponents( Button )
      const allActionButtons = allButtons.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( allActionButtons.length ).toBe( 0 )

    })

    it( 'sets the canEdit value to false when the status is Received', () => {
      expect( wrapper.vm.canEdit ).toBe( false )
    })

    it( 'sets the isPartial value to false when the status is Received', () => {
      expect( wrapper.vm.isPartial ).toBe( false )
    })

  })

})

describe( 'asn info - [component::AsnInfo]', () => {

  let wrapper = shallowMount( AsnInfo, {
    props: {
      canEdit: true,
    },
    stubs: {
      Sidebar,
    },
  })

  beforeEach( async () => {

    wrapper = shallowMount( AsnInfo, {
      props: {
        canEdit: true,
      },
      stubs: {
        Sidebar,
      },
    })
  })

  it( 'enables edit button when canEdit is true', () => {

    const editButton = wrapper.findComponent( Button )

    expect( editButton.exists()).toBe( true )
    expect( editButton.text()).toContain( 'Edit' )
    expect( editButton.props().disabled ).toBe( false )

  })

  it( 'disables edit button when canEdit is false', async () => {

    await wrapper.setProps({ canEdit: false })

    const editButton = wrapper.findComponent( Button )

    expect( editButton.exists()).toBe( true )
    expect( editButton.props().disabled ).toBe( true )

  })

  describe( 'edit details sidebar', () => {

    it( 'opens the Edit Details sidebar when edit button is clicked', async () => {

      const editButton = wrapper.findComponent( Button )

      expect( editButton.text()).toBe( 'Edit' )

      await editButton.trigger( 'click' )
      await wrapper.vm.$nextTick()

      const sidebars = wrapper.findAllComponents( Sidebar )
      const editSidebar = sidebars.find( s => s.props().name === 'edit-details' )

      expect( wrapper.vm.openEditAsnDetails ).toBe( true )
      expect( editSidebar.props().open ).toBe( true )

    })

    it( 'hides the Edit Details sidebar if openEditAsnDetails is false', async () => {

      const allSidebars = wrapper.findAllComponents( Sidebar )
      const sidebar = allSidebars.find( s => s.props().name === 'edit-details' )

      wrapper.vm.openEditAsnDetails = false

      expect( sidebar.exists()).toBe( true )
      expect( sidebar.props( 'open' )).toBe( false )

    })

    it( 'closes the edit sidebar when close event is emitted', async () => {

      const editButton = wrapper.findComponent( Button )

      await editButton.trigger( 'click' )

      expect( wrapper.vm.openEditAsnDetails ).toBe( true )

      wrapper.findComponent( Sidebar ).vm.$emit( 'close' )

      expect( editButton.text()).toBe( 'Edit' )
      expect( wrapper.vm.openEditAsnDetails ).toBe( false )

    })

  })

  describe( 'toggle actions button', () => {

    it( 'shows Actions button and sets openDesktopActions to true when canEdit is true', async () => {

      await wrapper.setProps({ canEdit: true })

      const allButtons = wrapper.findAllComponents( Button )
      expect( allButtons.length ).toBeGreaterThan( 0 )

      const actionsButton = allButtons.find( b => b.attributes()['data-button-desktop'] === 'toggle-actions' )

      expect( actionsButton.text()).toBe( 'Actions' )
      expect( wrapper.vm.openDesktopActions ).toBe( false )

      await actionsButton.trigger( 'click' )

      expect( wrapper.vm.openDesktopActions ).toBe( true )

    })

    it( 'hides Actions button when canEdit is false and isPartial is false', async () => {

      await wrapper.setProps({ canEdit: false, isPartial: false })
      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const actionsButton = allButtons.find( b => b.attributes()['data-button-desktop'] === 'toggle-actions' )

      expect( actionsButton ).toBe( undefined )

    })

  })

  describe( 'desktop Actions sidebar', () => {

    it( 'sets openDesktopActions to true when isPartial is true and canEdit is false', async () => {

      await wrapper.setProps({ canEdit: false, isPartial: true })

      const allButtons = wrapper.findAllComponents( Button )
      expect( allButtons.length ).toBeGreaterThan( 0 )

      const actionsButton = allButtons.find( b => b.attributes()['data-button-desktop'] === 'toggle-actions' )

      expect( actionsButton.text()).toBe( 'Actions' )
      expect( wrapper.vm.openDesktopActions ).toBe( false )

      await actionsButton.trigger( 'click' )

      expect( wrapper.vm.openDesktopActions ).toBe( true )

    })

  })

  it( 'renders ASN details correctly', () => {

    const detailsCards = wrapper.findAllComponents( DetailsCard )
    expect( detailsCards.length ).toBeGreaterThan( 0 )

    const shipperNameCard = detailsCards.find( card => card.props().label === 'Shipper Name' )
    expect( shipperNameCard.props().content ).toBe( mockAsn.shipperName )

    const facilityCard = detailsCards.find( card => card.props().label === 'Facility' )
    expect( facilityCard.props().content ).toBe( mockAsn.facility )

    const owdReferenceCard = detailsCards.find( card => card.props().label === 'OWD Reference' )
    expect( owdReferenceCard.props().content ).toBe( mockAsn.id )

    const autoReleaseCard = detailsCards.find( card => card.props().label === 'Auto Release' )
    expect( autoReleaseCard.props().content ).toBe( 'Yes' )

    const expectedDateCard = detailsCards.find( card => card.props().label === 'Expected Date' )
    expect( expectedDateCard.props().content ).toBe( mockAsn.expectedDate ? formatDate( mockAsn.expectedDate, 'MMM DD, YYYY' ) : '/' )

    const carrierCard = detailsCards.find( card => card.props().label === 'Carrier' )
    expect( carrierCard.props().content ).toBe( mockAsn.carrier )

    const cartonPackageCard = detailsCards.find( card => card.props().label === 'Carton/Package' )
    expect( cartonPackageCard.props().content ).toBe( typeof mockAsn.cartonCount === 'number' ? mockAsn.cartonCount : '/' )

    const palletCountCard = detailsCards.find( card => card.props().label === 'Pallet Count' )
    expect( palletCountCard.props().content ).toBe( typeof mockAsn.palletCount === 'number' ? mockAsn.palletCount : '/' )

    const clientReferenceCard = detailsCards.find( card => card.props().label === 'Client Reference' )
    expect( clientReferenceCard.props().content ).toBe( mockAsn.clientReference )

    const clientPoNumberCard = detailsCards.find( card => card.props().label === 'Client PO Number' )
    expect( clientPoNumberCard.props().content ).toBe( mockAsn.clientPo )

    const isBlindCard = detailsCards.find( card => card.props().label === 'Is Blind' )
    expect( isBlindCard.props().content ).toBe( booleanToYesNo( mockAsn.hasBlind ))

    const createdCard = detailsCards.find( card => card.props().label === 'Created' )
    expect( createdCard.props().content ).toBe( mockAsn.createdDate ? formatDate( mockAsn.createdDate, 'MMM DD, YYYY [at] HH:mm' ) : '/' )

    const createdByCard = detailsCards.find( card => card.props().label === 'Created By' )
    expect( createdByCard.props().content ).toBe( mockAsn.createdBy )

    const instructionNotesCard = detailsCards.find( card => card.props().label === 'Instruction Notes' )
    expect( instructionNotesCard.props().content ).toBe( '/' )

  })

})

describe( 'asn details - [component:AsnDetailsForm]', () => {
  let wrapper

  beforeEach(() => {

    wrapper = mount( AsnDetailsForm )
  })

  it( 'renders the correct asnId in the header', () => {
    const header = wrapper.find( '[data-element="header"]' )
    expect( header.exists()).toBe( true )
    expect( header.text()).toBe( 'Edit [159608]' )
  })

  it( 'renders all fields', () => {
    const allInputs = wrapper.findAllComponents( InputField )
    const allSelects = wrapper.findAllComponents( '[data-input-type="select"]' )
    const allDatePickers = wrapper.findAllComponents( DatePicker )
    const allCheckboxes = wrapper.findAllComponents( Checkbox )

    expect( allInputs[0].props().label ).toBe( 'Shipper Name' )
    expect( allSelects[0].props().label ).toBe( 'Facility' )
    expect( allDatePickers[0].props().label ).toBe( 'Expected Date' )
    expect( allSelects[1].props().label ).toBe( 'Carrier' )
    expect( allInputs[3].props().label ).toBe( 'Client Reference' )
    expect( allInputs[4].props().label ).toBe( 'Client PO Number' )
    expect( allInputs[5].props().label ).toBe( 'Carton/Package Count' )
    expect( allInputs[6].props().label ).toBe( 'Pallet Count' )
    expect( allInputs[7].props().label ).toBe( 'Instruction Notes' )
    expect( allCheckboxes[0].exists()).toBe( true )
    expect( allCheckboxes[0].text()).toBe( 'Auto Release' )
  })

  it( 'validates required fields', async () => {

    Object.keys( asn.value ).forEach(( key ) => {
      asn.value[key] = null
    })
    const newWrapper = mount( AsnDetailsForm )
    const allButtons = newWrapper.findAllComponents( Button )
    const saveButton = allButtons.find( b => b.attributes()['data-button'] === 'save-changes' )

    expect( saveButton.text()).toBe( 'Save Changes' )

    expect( saveButton.props().disabled ).toBe( true )

    // Fill required fields

    const dateInput     = newWrapper.findAllComponents( DatePicker )[0] as any
    const shipperInput  = newWrapper.findAllComponents( InputField )[0] as any
    const carrierInput  = newWrapper.findAllComponents( '[data-input-type="select"]' )[1] as any
    const facilityInput = newWrapper.findAllComponents( '[data-input-type="select"]' )[0] as any

    await dateInput.vm.$emit( 'update:modelValue', '2025-01-25' )
    await shipperInput.vm.$emit( 'update:modelValue', 'Test Shipper' )
    await carrierInput.vm.$emit( 'update:modelValue', 'Unknown' )
    await facilityInput.vm.$emit( 'update:modelValue', 'DC1' )

    await newWrapper.vm.$nextTick()

    expect( saveButton.props().disabled ).toBe( false )
  })

  it( 'handles numeric inputs correctly', async () => {

    const cartonInput = wrapper.findAllComponents( InputField )[5]
    const palletInput = wrapper.findAllComponents( InputField )[6]

    // Test numeric validation
    await cartonInput.vm.$emit( 'update:modelValue', '123' )
    await palletInput.vm.$emit( 'update:modelValue', '456' )

    expect( wrapper.vm.asnFormModel.cartonCount.value ).toBe( '123' )
    expect( wrapper.vm.asnFormModel.palletCount.value ).toBe( '456' )

  })

  it( 'handles form submission', async () => {

    const submitSpy =  vi.spyOn( wrapper.vm, 'submitAsn' )

    const dateInput     = wrapper.findAllComponents( DatePicker )[0] as any
    const shipperInput  = wrapper.findAllComponents( InputField )[0] as any
    const carrierInput  = wrapper.findAllComponents( '[data-input-type="select"]' )[1] as any
    const facilityInput = wrapper.findAllComponents( '[data-input-type="select"]' )[0] as any

    // Fill and validate required fields
    await dateInput.vm.$emit( 'update:modelValue', '2025-01-25' )
    await shipperInput.vm.$emit( 'update:modelValue', 'Test Shipper' )
    await carrierInput.vm.$emit( 'update:modelValue', 'Unknown' )
    await facilityInput.vm.$emit( 'update:modelValue', 'DC1' )

    const allButtons = wrapper.findAllComponents( Button )
    const saveButton = allButtons.find( b => b.attributes()['data-button'] === 'save-changes' )

    expect( saveButton.props().disabled ).toBe( false )

    Object.keys( wrapper.vm.asnFormModel ).forEach(( key ) => {
      expect( wrapper.vm.asnFormModel[key].valid ).toBe( true )
    })

    await saveButton.trigger( 'click' )

    expect( submitSpy ).toHaveBeenCalled()
  })

  it( 'disables facility selection when defaultFacility is not ALL', async () => {
    defaultFacility.value = 'DC1'

    const wrapper = mount( AsnDetailsForm )
    const facilitySelect = wrapper.findAllComponents( '[data-input-type="select"]' )[0] as any

    expect( facilitySelect.props().readonly ).toBe( true )
  })

  it( 'handles auto-release checkbox toggle', async () => {
    const checkbox = wrapper.findComponent( Checkbox )

    await checkbox.vm.$emit( 'update:modelValue', true )
    expect( wrapper.vm.asnFormModel.isAutoRelease.value ).toBe( true )

    await checkbox.vm.$emit( 'update:modelValue', false )
    expect( wrapper.vm.asnFormModel.isAutoRelease.value ).toBe( false )
  })

  it( 'emits close event when close button is clicked', async () => {
    const closeButton = wrapper.findAllComponents( Button )[0]
    await closeButton.trigger( 'click' )

    expect( wrapper.emitted().close ).toBeTruthy()
    expect( wrapper.emitted().close ).toHaveLength( 1 )
  })
})
