<script setup lang="ts">

import { useRoute } from 'vue-router'
import { jwtDecode } from 'jwt-decode'
import { loginRequest, msalInstance } from '@lib/auth/scripts/authConfig'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { clientId, userFirstName, userLastName, userMail, userName } from '@/modules/auth/store'

import {
  announcementsList,
  appMode,
  clearOldCacheEntries,
  clientsList,
  countriesList,
  defaultCountry,
  defaultFacility,
  facilitiesList,
  facilitiesLookup,
  getAnnouncements,
  getClients,
  getCountries,
  getFacilities,
  getShippingMethods,
  productDetailsOptions,
  shippingMethods,
  shippingMethodsList,
  showAnnouncementModal
} from '@/store'

import Loader from '@lib/components/blocks/Loader.vue'
import Header from '@/components/Header.vue'
import Navbar from '@/components/Navbar.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Announcement from '@/components/Announcement.vue'
import InventoryProductDetails from '@/components/InventoryProductDetails.vue'

import type { DecodedToken } from '@/types'

const route         = useRoute()
const pending       = ref<boolean>( true )
const toggleMenu    = ref<boolean>( false )

watch( route, () => toggleMenu.value = false )

function closeProductDetails() {
  productDetailsOptions.id = null
  productDetailsOptions.type = null
}

function extractClientInitials( clientName: string ) {

  let words = clientName.split( ' ' )

  words = words.filter( word => ![
    'Inc.',
    'Inc',
    'INC',
    'INC.',
    'Incorporated',
    'Incorporated.',
    'LLC',
    'LLC.',
    'Llc',
    'Llc.',
    'Corp',
    'CORP',
    'Corporate'
  ].includes( word ))

  return words.map( word => word[0] ).join( '' )

}

async function getClientList() {

  if ( appMode !== 'ADMIN' )
    return

  const { payload, error } = await getClients()

  if ( !error ) {

    clientsList.value = payload.map( c => ({
      id:          c.client_id,
      name:        c.company_name,
      description: extractClientInitials( c.company_name )
    }))

  }

}

async function getTenantProperties() {

  // Get the access token from the MSAL instance.
  // We have to acquire the token first when the app mounts,
  // so that we can use the clientId from it to make requests to the API.

  const { accessToken } = await msalInstance.acquireTokenSilent( loginRequest )

  const decodedToken = jwtDecode<DecodedToken>( accessToken )

  userMail.value = decodedToken.signInName
  userName.value = decodedToken.name
  userLastName.value = decodedToken.family_name
  clientId.value = decodedToken.sci
  userFirstName.value = decodedToken.given_name

}

const cacheTimeout = ref<NodeJS.Timeout>( null )

async function getAppDependencies() {

  // Clear old cache after one hour interval.

  clearInterval( cacheTimeout.value )
  cacheTimeout.value = setInterval( clearOldCacheEntries, 3600000 )

  pending.value = true

  // Get the dependencies.

  const [ countriesData, facilitiesData, announcementsData, shippingMethodsData ] = await Promise.all( [
    getCountries(),
    getFacilities(),
    getAnnouncements(),
    getShippingMethods(),
    getClientList()
  ] )

  // COUNTRIES
  // Map the countries to droplist options and remove duplicate ids.
  // If there is a default country, set it.

  countriesList.value = countriesData.payload?.countries
    ?.filter(( country, index, self ) => self.findIndex( c => c.displayName === country.displayName ) === index )
    ?.map( c => ({ id: c.displayName, name: c.displayName })) ?? []

  defaultCountry.value = countriesData.payload?.countries?.find( c => c.isDefault )?.displayName ?? null

  // FACILITIES
  // Map the facilities to droplist options.
  // If there is a default facility, set it.

  facilitiesList.value = facilitiesData.payload?.facilities?.map( f => ({ id: f.code, name: f.display })) ?? []
  defaultFacility.value = facilitiesData.payload?.defaultFacility ?? 'ALL'
  facilitiesLookup.value = facilitiesData.payload?.facilities ?? []

  // If the default facility is not 'ALL', then filter the
  // facilities list to only include the default facility.

  if ( defaultFacility.value !== 'ALL' )
    facilitiesList.value = facilitiesList.value.filter( f => f.id === defaultFacility.value )

  // SHIPPING METHODS
  // Set the shipping methods to the store.
  // Map the shipping methods to droplist options.

  shippingMethods.value = shippingMethodsData.payload?.shippingMethods ?? []
  shippingMethodsList.value = shippingMethodsData.payload?.shippingMethods?.map( s => ({ id: s.value, name: s.name })) ?? []

  // ANNOUNCEMENTS
  // Filter the announcements to only include those that are published.

  announcementsList.value = announcementsData?.payload?.filter( announcement => announcement.status === 'publish' ) ?? []

  pending.value = false

}

onMounted( async () => {
  getTenantProperties()
  getAppDependencies()
})

watch( clientId, () => getAppDependencies())
onBeforeUnmount(() => clearInterval( cacheTimeout.value ))

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <Header v-model:open-menu="toggleMenu" :pending="pending" />

    <div v-if="pending" class="w-full h-full flex items-center justify-center space-x-2">
      <Loader name="" />
    </div>

    <div v-else class="w-full relative grid grid-cols-[max-content_1fr] overflow-hidden">

      <Navbar v-model:toggle="toggleMenu" />

      <div class="w-full h-full max-h-full overflow-y-auto" @click="toggleMenu = false">

        <RouterView v-slot="{ Component }">

          <Transition mode="out-in" name="route">

            <Component :is="Component" />

          </Transition>

        </RouterView>

      </div>

    </div>

    <Sidebar :dim="true" :open="showAnnouncementModal" @close="showAnnouncementModal = false">

      <Announcement />

    </Sidebar>

    <Sidebar
      :open="!!productDetailsOptions.id"
      :dim="true"
      :strict="true"
      :z-index="70"
      position="left"
      :fit-content="true"
      @close="closeProductDetails"
    >

      <InventoryProductDetails
        :item-id="productDetailsOptions.id"
        :type="productDetailsOptions.type"
        @close="closeProductDetails"
      />

    </Sidebar>

  </div>

</template>
