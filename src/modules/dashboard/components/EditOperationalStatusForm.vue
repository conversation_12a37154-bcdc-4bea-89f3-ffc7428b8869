<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { updateFacility } from '@/modules/dashboard/store'
import { facilitiesLookup } from '@/store'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref } from 'vue'
import { convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

import type { FacilityOperationalStatus } from '@/modules/dashboard/types'

const props = defineProps<{
  facilityReference: string
}>()

const emits = defineEmits<Emits>()

interface Emits {
  ( eventName: 'close' ): void
}

const pending   = ref( false )
const facility  = ref<FacilityOperationalStatus>( facilitiesLookup.value.find( item => item.code === props.facilityReference ))

const editFacilityModel: FacilityOperationalStatus = {
  code:                  facility.value?.code,
  display:               facility.value?.display,
  isDefault:             facility.value?.isDefault,
  description:           facility.value?.description,
  operationalTitle:      facility.value?.operationalTitle,
  operationalPercentage: facility.value?.operationalPercentage,
}

const facilityModel = reactive<Validatable<FacilityOperationalStatus>>( convertObjectToValidatable( editFacilityModel, null, [ 'code', 'display', 'isDefault' ] ))
const canUpdate     = computed(() => validateModel( facilityModel ))

async function updateItem() {

  pending.value = true

  const facility = convertObjectToPlain( facilityModel )

  const { error, payload } = await updateFacility( editFacilityModel.code, facility )

  if ( !error ) {

    facilitiesLookup.value = payload.facilities

    setNotificationOptions({ message: 'Facility is updated successfully.' })

    emits( 'close' )

  }

  pending.value = false

}

</script>

<template>

  <div class="h-full overflow-hidden grid grid-rows-[max-content_1fr_max-content_max-content] relative">

    <div class="w-full h-12 flex items-center border-b border-border-subtle-00">

      <div class="h-full px-4 flex items-center grow">

        <p class="text-sm font-medium">
          <span>Edit Facility:<span class="text-main"> [{{ facilityModel.code.value }}]</span></span>
        </p>

      </div>

      <Button
        type="box"
        mode="ghost"
        icon="close"
        :disabled="pending"
        @click="emits('close')"
      />

    </div>

    <form
      class="w-full md:w-[34rem] md:p-6 grid md:gap-4 content-start overflow-y-auto"
      :class="{
        'pointer-events-none': pending,
      }"
      @submit.prevent
    >

      <InputField
        v-model="facilityModel.operationalPercentage.value"
        v-model:valid="facilityModel.operationalPercentage.valid"
        type="number"
        label="Functional Percentage"
        :strict="true"
        :required="true"
        :validation-options="{ min: 0, max: 100 }"
      />

      <InputField
        v-model="facilityModel.operationalTitle.value"
        v-model:valid="facilityModel.operationalTitle.valid"
        type="textarea"
        label="Reduced Operations Cause (max 50 characters)"
        :required="facilityModel.operationalPercentage.value !== 100 && true"
      />

      <InputField
        v-model="facilityModel.description.value"
        v-model:valid="facilityModel.description.valid"
        type="textarea"
        label="Description (max 50 characters)"
        :required="false"
      />

    </form>

    <div class="w-full h-12 bg-layer-01 border-t border-border-subtle-00">

      <Button
        :pending="pending"
        :disabled="!canUpdate"
        @click="updateItem"
      >
        Update Facility
      </Button>

    </div>

  </div>

</template>
