<script setup lang="ts">

import { onMounted, ref } from 'vue'
import { getOrdersByStatus } from '@/modules/orders/store'

import KpiPanel from '@/modules/dashboard/components/KpiPanel.vue'
import GlobePanel from '@/modules/dashboard/components/GlobePanel.vue'
import StatusPanel from '@/modules/dashboard/components/StatusPanel.vue'

import type { OrdersByStatusPayload } from '@/modules/orders/types'

const ordersByStatus = ref<OrdersByStatusPayload>({
  onHold:                   [],
  voided:                   [],
  shipped:                  [],
  unknown:                  [],
  backordered:              [],
  atWarehouse:              [],
  totalNumberOrders:        0,
  shippingMethodKeys:       [],
  fulfillmentStatusPercent: 0
})

onMounted( async () => {

  const { payload } = await getOrdersByStatus()

  ordersByStatus.value = payload

})

</script>

<template>

  <div class="isolate h-full relative lg:grid lg:grid-rows-[max-content_1fr] lg:grid-cols-2 2xl:grid-cols-[1fr_max-content] bg-layer-02 overflow-hidden overflow-y-auto lg:overflow-y-hidden xl:overflow-y-hidden">

    <StatusPanel :orders-by-status="ordersByStatus" class="lg:bg-layer-01 absolute top-0 left-0 z-1 lg:relative lg:col-span-2 xl:col-span-1 lg:border-b border-border-subtle-00" />

    <GlobePanel class="xl:w-full 2xl:w-[40rem] lg:min-h-full h-[80%] lg:row-start-2 xl:row-span-2 lg:col-start-2 xl:col-start-auto relative" />

    <KpiPanel :orders-by-status="ordersByStatus" class="lg:col-start-1 lg:row-span-1" />

  </div>

</template>

<style>
.cds--cc--gauge path.arc-background {
  fill: var(--border-subtle-00) !important;
}
</style>
