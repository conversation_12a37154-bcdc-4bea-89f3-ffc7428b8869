import { mount } from '@vue/test-utils'
import { updateDraftOrder } from '@/modules/orders/store'
import { describe, expect, it, vi } from 'vitest'
import { defaultFacility, facilitiesList } from '@/store'

import Radio from '@lib/components/inputs/Radio.vue'
import Button from '@lib/components/button/Button.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import EditDetailsForm from '@/modules/orders/views/draft-details/components/EditDetailsForm.vue'
import EditOrderDetailsForm from '@/modules/orders/views/draft-details/components/EditOrderDetailsForm.vue'

import type { DraftOrder } from '@/modules/orders/types'

const mockDetailsProp: DraftOrder = {
  id:              12,
  clientReference: 'client-ref-1',
  importStatus:    'Draft',
  isGift:          false,
  giftMessage:     null,
  source:          'Shopify',
  poNumber:        'PO12345',
  groupName:       'Group A',
  facilityCode:    'DC6',
  backorderRule:   'BACKORDER',
  isDdp:           false,
  holdForRelease:  false,
  isBusinessOrder: true,
  shipHandlingFee: 10,
  shippingMethod:  'OWD_USPS_I_PRIORITY',
  billing:         {
    firstName:   'Alice',
    lastName:    'Doe',
    companyName: 'Doe Inc',
    address1:    '123 Main St',
    address2:    '456 Main St',
    city:        'Anytown',
    state:       'CA',
    zip:         '12345',
    country:     'USA',
    phone:       '5555551234',
    email:       '<EMAIL>',
    paymentType: 'CLIENT',
  },
  shipping: {
    firstName:   'Jane',
    lastName:    'Doe',
    companyName: 'Other Doe Inc',
    address1:    '123 Oak St',
    address2:    '456 Oak St',
    city:        'Anytown',
    state:       'CA',
    zip:         '12345',
    country:     'USA',
    phone:       '555-5678',
    fax:         null,
    shipType:    'OWD_USPS_I_PRIORITY',
    email:       '<EMAIL>',
  },
  masterRecordId:     null,
  importErrorCount:   0,
  importErrors:       null,
  createdTs:          '2024-09-27T10:39:31.6560298+00:00',
  createdByUserId:    '<EMAIL>',
  createdByUsername:  'Birdie Love Portal Test User',
  modifiedTs:         null,
  modifiedByUserId:   null,
  modifiedByUsername: null
}

vi.mock( '@/store', async ( importOriginal ) => {

  const actual = await importOriginal() as any

  actual.defaultCountry.value = 'USA'
  actual.defaultFacility.value = 'DC1'
  actual.facilitiesList.value = [
    {
      id:   'DC1',
      name: 'Midwest - Mobridge, SD'
    }
  ]

  return {
    ...actual
  }

})

vi.mock( '@/modules/orders/store', async ( importOriginal ) => {
  const originalModule = await importOriginal() as any

  return {
    ...originalModule,
    updateDraftOrder: vi.fn(() =>
      Promise.resolve({
        error:   null,
        status:  200,
        headers: {
          'content-encoding':  'gzip',
          'content-type':      'application/json; charset=utf-8',
          'date':              'Fri, 18 Oct 2024 10:49:34 GMT',
          'request-context':   'appId=cid-v1:271575a9-4199-40c2-afa4-66b574cb6e2f',
          'transfer-encoding': 'chunked',
          'vary':              'Accept-Encoding',
        },
        payload: {
          id:              12,
          clientReference: 'client-ref-1',
          importStatus:    'Draft',
          isGift:          false,
          giftMessage:     null,
          source:          'Shopify',
          poNumber:        'PO12345',
          groupName:       'Group A',
          facilityCode:    'DC6',
          backorderRule:   'BACKORDER',
          isDdp:           false,
          holdForRelease:  false,
          isBusinessOrder: true,
          shipHandlingFee: 10,
          shippingMethod:  'OWD_USPS_I_PRIORITY',
          billing:         {
            firstName:   'Alice',
            lastName:    'Doe',
            companyName: 'Doe Inc',
            address1:    '123 Main St',
            address2:    '456 Main St',
            city:        'Anytown',
            state:       'CA',
            zip:         '12345',
            country:     'USA',
            phone:       '5555551234',
            email:       '<EMAIL>',
            paymentType: 'CLIENT',
          },
          shipping: {
            firstName:   'Jane',
            lastName:    'Doe',
            companyName: 'Other Doe Inc',
            address1:    '123 Oak St',
            address2:    '456 Oak St',
            city:        'Anytown',
            state:       'CA',
            zip:         '12345',
            country:     'USA',
            phone:       '555-5678',
            fax:         null,
            email:       '<EMAIL>',
          },
          masterRecordId:     null,
          importErrorCount:   0,
          importErrors:       null,
          createdTs:          '2024-09-27T10:39:31.6560298+00:00',
          createdByUserId:    '<EMAIL>',
          createdByUsername:  'Birdie Love Portal Test User',
          modifiedTs:         '2024-10-18T10:49:34.9811476+00:00',
          modifiedByUserId:   '<EMAIL>',
          modifiedByUsername: 'Birdie Love Portal Test User',
        },
      })
    ),
    internalTaxOptions: [
      {
        id:   1,
        name: 'Delivery Dispatch Import',
      },
      {
        id:   0,
        name: 'Delivered Duty Unpaid',
      },
    ],

  }
})

describe( 'draft order details - [ component::EditDetailsForm ]', () => {
  // Create a div to serve as the teleport target
  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const wrapper = mount( EditDetailsForm, {
    attachTo: document.body,
    props:    { order: mockDetailsProp, pending: false }
  })

  it( 'renders the edit details component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'draft details forms', () => {

    it( 'sets the "Billing details are same as shipping" checkbox to false by default', () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allCheckboxes = shippingSection.findAllComponents( Checkbox )

      expect( allCheckboxes[0].text()).toBe( 'Billing details are same as shipping' )
      expect( allCheckboxes[0].props().modelValue ).toBe( false )

    })

    it( 'renders all the fields with the correct values in the Billing details section', () => {

      const billingSection = wrapper.find( '[data-section="billing"]' )

      const allInputs = billingSection.findAllComponents( InputField )
      const allSelects = billingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Alice' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Doe' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Doe Inc' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '123 Main St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '456 Main St' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'CA' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'Anytown' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '12345' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '5555551234' )

    })

    it( 'renders all the fields with the correct values in the Shipping details section', () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allInputs = shippingSection.findAllComponents( InputField )
      const allSelects = shippingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Jane' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Doe' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Other Doe Inc' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '123 Oak St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '456 Oak St' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'CA' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'Anytown' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '12345' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '555-5678' )

    })

    it( 'renders all the fields with the correct values in the Delivery details section', () => {

      const deliverySection = wrapper.find( '[data-section="delivery"]' )

      const allSelects = deliverySection.findAllComponents( '[data-input-type="select"]' )

      // Shipping Method
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().modelValue ).toBe( 'OWD_USPS_I_PRIORITY' )
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().nullable ).toBe( false )

    })

    it( 'copies the shipping details to billing details when "Billing details are same as shipping" checkbox to set to true', async () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allCheckboxes = shippingSection.findAllComponents( Checkbox )

      const sameDetailsCheckbox = allCheckboxes[0]
      // The label is the clickable element in the checkbox
      const sameDetailsCheckboxLabel = allCheckboxes[0].find( 'label' )

      // Click the label to copy the shipping info into billing
      await sameDetailsCheckboxLabel.trigger( 'click' )
      await wrapper.vm.$nextTick()

      // Confirm that it's the correct checkbox
      expect( sameDetailsCheckbox.text()).toBe( 'Billing details are same as shipping' )

      // Confirm that the value is changed
      expect( sameDetailsCheckbox.props().modelValue ).toBe( true )

      const billingSection = wrapper.find( '[data-section="billing"]' )

      const allInputs = billingSection.findAllComponents( InputField )
      const allSelects = billingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Jane' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Doe' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Other Doe Inc' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '123 Oak St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '456 Oak St' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'CA' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'Anytown' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '12345' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '555-5678' )

    })

  })

  describe( 'update draft order button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

    it( 'sets Save Info button disabled to false and canUpdate to true if all the details are correct', () => {

      expect( wrapper.vm.canUpdate ).toBe( true )
      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

    })

    it( 'sets Save Info button disabled to true and sets canUpdate to false if at least one of the required fields is not valid', async () => {

      // set one required field to empty
      wrapper.vm.billingModel.firstName.value = ''

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.billingModel.firstName.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

    })

    it( 'calls updateOrderDetails and updateDraftOrder when the Save Info button is clicked', async () => {

      const updateOrderDetailsSpy = vi.spyOn( wrapper.vm, 'updateOrderDetails' )

      // Ensure the required fields are valid to enable the button
      wrapper.vm.billingModel.firstName.value = 'John'
      await wrapper.vm.$nextTick()

      // Find the Save Info button
      const allButtons = wrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.text() === 'Save Info' )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

      await updateButton.trigger( 'click' )

      expect( updateOrderDetailsSpy ).toHaveBeenCalled()

      const expectedPayload = {
        backorderRule: 'BACKORDER',
        billing:       {
          address1:    '123 Oak St',
          address2:    '456 Oak St',
          city:        'Anytown',
          companyName: 'Other Doe Inc',
          country:     'USA',
          email:       '<EMAIL>',
          firstName:   'Jane',
          lastName:    'Doe',
          paymentType: 'CLIENT',
          phone:       '555-5678',
          state:       'CA',
          zip:         '12345',
        },
        clientReference:    'client-ref-1',
        createdByUserId:    '<EMAIL>',
        createdByUsername:  'Birdie Love Portal Test User',
        createdTs:          '2024-09-27T10:39:31.6560298+00:00',
        facilityCode:       'DC6',
        giftMessage:        null,
        groupName:          'Group A',
        holdForRelease:     false,
        id:                 12,
        importErrorCount:   0,
        importErrors:       null,
        importStatus:       'Draft',
        isBusinessOrder:    true,
        isDdp:              false,
        isGift:             false,
        masterRecordId:     null,
        modifiedByUserId:   null,
        modifiedByUsername: null,
        modifiedTs:         null,
        poNumber:           'PO12345',
        shipHandlingFee:    10,
        shipping:           {
          address1:    '123 Oak St',
          address2:    '456 Oak St',
          city:        'Anytown',
          companyName: 'Other Doe Inc',
          country:     'USA',
          email:       '<EMAIL>',
          fax:         null,
          firstName:   'Jane',
          lastName:    'Doe',
          phone:       '555-5678',
          shipType:    'OWD_USPS_I_PRIORITY',
          state:       'CA',
          zip:         '12345',
        },
        shippingMethod: 'OWD_USPS_I_PRIORITY',
        source:         'Shopify',
      }

      // Validate the payload passed to updateDraftOrder
      expect( updateDraftOrder ).toHaveBeenCalled()
      expect( updateDraftOrder ).toHaveBeenCalledWith( 12, expectedPayload )

      // Verify the component emitted the 'update' event exactly once
      expect( wrapper.emitted()).toHaveProperty( 'update' )
      expect( wrapper.emitted( 'update' )?.length ).toBe( 1 )

    })

  })

  describe( 'cancel button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const cancelButton = allButtons.find( b => b.attributes()['data-button'] === 'cancel' )

    it( 'renders the cancel button', () => {
      expect( cancelButton.exists()).toBe( true )
    })

    it( 'fires close event when the cancel button is clicked', async () => {

      expect( cancelButton.exists()).toBe( true )

      await cancelButton.trigger( 'click' )

      expect( wrapper.emitted()).toHaveProperty( 'close' )
      expect( wrapper.emitted( 'close' )?.length ).toBe( 1 )

    })

  })

  describe( 'models validity', () => {

    describe( 'billingModel validity', () => {

      const allButtons = wrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

      it( 'sets all valid keys in billingModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

        const billingModel = wrapper.vm.billingModel

        // Check that every `valid` property in each field is true
        const allValid = Object.values( billingModel ).every(
          field => field.valid === true
        )
        expect( wrapper.vm.canUpdate ).toBe( true )
        expect( allValid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )

      })

      it( 'sets valid keys on non-mandatory fields in billingModel to true, canEdit to true and enable the update button when their values are null', async () => {

        // Set specified mandatory fields to null

        const nonMandatoryFields = [ 'companyName', 'address2', 'email', 'phone' ]

        nonMandatoryFields.forEach(( field ) => {
          wrapper.vm.billingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const billingModel = wrapper.vm.billingModel

        // Check only specified keys for `valid` being `false`
        const allNonMandatoryFields = nonMandatoryFields.every(
          field => billingModel[field].valid === true
        )
        expect( wrapper.vm.canUpdate ).toBe( true )
        expect( allNonMandatoryFields ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )

      })

      it( 'sets valid keys on mandatory fields in billingModel to false, canUpdate to false and disable the update button when their values are null', async () => {

        // Set specified mandatory fields to null
        const mandatoryFields = [ 'firstName', 'lastName', 'address1', 'country', 'state', 'city', 'zip' ]
        mandatoryFields.forEach(( field ) => {
          wrapper.vm.billingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const billingModel = wrapper.vm.billingModel

        // Check only specified keys for `valid` being `false`
        const allMandatoryFieldsInvalid = mandatoryFields.every(
          field => billingModel[field].valid === false
        )
        expect( wrapper.vm.canUpdate ).toBe( false )
        expect( allMandatoryFieldsInvalid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( true )

      })

    })
    describe( 'shippingModel validity', () => {

      // update the wrapper
      const newWrapper = mount( EditDetailsForm, {
        attachTo: document.body,
        props:    { order: mockDetailsProp, pending: false }
      })

      const allButtons = newWrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

      it( 'sets all valid keys in shippingModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

        const shippingModel = newWrapper.vm.shippingModel

        // Check that every `valid` property in each field is true
        const allValid = Object.values( shippingModel ).every(
          field => field.valid === true
        )

        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allValid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )

      })

      it( 'sets valid keys on non-mandatory fields in shippingModel to true, canUpdate to true and enable the update button when their values are null', async () => {

        // Set specified mandatory fields to null

        const nonMandatoryFields = [ 'companyName', 'address2', 'email', 'phone' ]

        nonMandatoryFields.forEach(( field ) => {
          wrapper.vm.shippingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const shippingModel = wrapper.vm.shippingModel

        // Check only specified keys for `valid` being `false`
        const allNonMandatoryFields = nonMandatoryFields.every(
          field => shippingModel[field].valid === true
        )
        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allNonMandatoryFields ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )

      })

      it( 'sets valid keys on mandatory fields in shippingModel to false, canUpdate to false and disable the update button when their values are null', async () => {

        // Set specified mandatory fields to null
        const mandatoryFields = [ 'firstName', 'lastName', 'address1', 'shipType', 'country', 'state', 'city', 'zip' ]

        mandatoryFields.forEach(( field ) => {
          newWrapper.vm.shippingModel[field].value = null
        })

        await newWrapper.vm.$nextTick()

        const shippingModel = newWrapper.vm.shippingModel

        // Check only specified keys for `valid` being `false`
        const allMandatoryFieldsInvalid = mandatoryFields.every(
          field => shippingModel[field].valid === false
        )
        expect( newWrapper.vm.canUpdate ).toBe( false )
        expect( allMandatoryFieldsInvalid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( true )

      })

    })

  })

})

describe( 'draft order details - [ component::EditOrderDetailsForm ]', () => {

  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const wrapper = mount( EditOrderDetailsForm, {
    attachTo: document.body,
    props:    {
      order: mockDetailsProp
    }
  })

  it( 'renders the order details form', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'details form fields', () => {

    const allInputs = wrapper.findAllComponents( InputField ) as any[]
    const backorderRule = wrapper.findComponent( '[data-input-type="select"]' ) as any
    const textBoxFields = wrapper.findAllComponents( '[data-input-type="textarea"]' ) as any[]
    const allRadios = wrapper.findAllComponents( Radio ) as any[]

    it( 'renders all the fields with the correct values and correct required prop', () => {

      // Client Reference Number
      const clientReferenceNumber = allInputs.find( i => i.props().label === 'Client Reference Number' )
      expect( clientReferenceNumber.exists()).toBe( true )
      expect( clientReferenceNumber.props().modelValue ).toBe( 'client-ref-1' )
      expect( clientReferenceNumber.props().required ).toBe( true )

      // PO Number
      const poNumber = allInputs.find( i => i.props().label === 'PO Number' )
      expect( poNumber.exists()).toBe( true )
      expect( poNumber.props().modelValue ).toBe( 'PO12345' )
      expect( poNumber.props().required ).toBe( false )

      // Backorder Rule
      expect( backorderRule.exists()).toBe( true )
      expect( backorderRule.props().nullable ).toBe( false )
      expect( backorderRule.props().modelValue ).toBe( 'BACKORDER' )

      // Shipment Facility
      const shipmentFacility = allRadios.find( r => r.props().label === 'Shipment Facility' )
      expect( shipmentFacility.exists()).toBe( true )
      expect( shipmentFacility.props().nullable ).toBe( false )
      expect( shipmentFacility.props().required ).toBe( true )
      expect( shipmentFacility.props().disabled ).toBe( true )
      expect( shipmentFacility.props().modelValue ).toBe( 'DC1' )

      // For users with default facility, the radio button should be hidden
      expect( shipmentFacility.attributes().style ).toBe( 'display: none;' )

      // Order Type
      const orderType  = allRadios.find( r => r.props().label === 'Order Type' )
      expect( orderType.exists()).toBe( true )
      expect( orderType.props().modelValue ).toBe( 1 )
      expect( orderType.props().required ).toBe( true )
      expect( orderType.props().nullable ).toBe( false )

    })

    it( 'renders and enables the Shipment Facility radio with all options', async () => {

      defaultFacility.value = 'ALL'
      facilitiesList.value = [
        {
          id:   'DC1',
          name: 'Midwest - Mobridge, SD'
        },
        {
          id:   'DC6',
          name: 'West - Los Angeles, CA'
        },
        {
          id:   'DC7',
          name: 'East - Wilmington, OH'
        }
      ]

      // The logic for facility is on mount we are not watching for changes. That's why we need to mount the wrapper again.
      const newWrapper = mount( EditOrderDetailsForm, {
        props: {
          order: mockDetailsProp
        }
      })

      await newWrapper.vm.$nextTick()
      const allRadios1 = newWrapper.findAllComponents( Radio )

      // Shipment Facility
      const shipmentFacility = allRadios1.find( r => r.props().label === 'Shipment Facility' )
      expect( shipmentFacility.exists()).toBe( true )
      expect( shipmentFacility.props().nullable ).toBe( false )
      expect( shipmentFacility.props().required ).toBe( true )
      expect( shipmentFacility.props().disabled ).toBe( false )
      expect( shipmentFacility.props().modelValue ).toBe( 'DC6' )

      // For users with multiple facilities, the radio button should be shown and style should not be display none
      expect( shipmentFacility.attributes().style ).toBeUndefined()
      expect( shipmentFacility.props().options ).toStrictEqual(
        [
          {
            id:   'DC1',
            name: 'Midwest - Mobridge, SD',
          },
          {
            id:   'DC6',
            name: 'West - Los Angeles, CA',
          },
          {
            id:   'DC7',
            name: 'East - Wilmington, OH',
          }
        ]
      )

    })

    it( 'hides the Gift Message input when "Use Gift Invoice" is not selected', async () => {

      wrapper.vm.orderDetailsModel.isGift.value = false

      await wrapper.vm.$nextTick()

      const giftMessageField = textBoxFields.find( t => t?.props()?.label === 'Optional Gift Message (up to size of box)' )

      expect( giftMessageField ).toBe( undefined )

    })

    it( 'renders the Gift Message input when "Use Gift Invoice" is selected', async () => {

      wrapper.vm.orderDetailsModel.isGift.value = true

      await wrapper.vm.$nextTick()

      const textBoxFields = wrapper.findAllComponents( '[data-input-type="textarea"]' ) as any[]
      const giftMessageField = textBoxFields.find( t => t?.props()?.label === 'Optional Gift Message (up to size of box)' )

      expect( giftMessageField.exists()).toBe( true )

    })

  })

  describe( 'update draft order(Save Info) button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

    it( 'sets Save Info button disabled to false and canUpdate to true if all the details are correct', () => {

      expect( wrapper.vm.canUpdate ).toBe( true )
      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

    })

    it( 'sets Save Info button disabled to false and sets canUpdate to false if at least one of the required fields is not valid', async () => {

      // set one field to empty
      wrapper.vm.orderDetailsModel.backorderRule.value = null

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.orderDetailsModel.backorderRule.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

    })

    it( 'calls updateOrderDetails and updateDraftOrder when the Save Info button is clicked', async () => {

      const updateOrderDetailsSpy = vi.spyOn( wrapper.vm, 'updateOrderDetails' )

      // Ensure the required fields are valid to enable the button
      wrapper.vm.orderDetailsModel.backorderRule.value = 'BACKORDER'

      await wrapper.vm.$nextTick()

      // Find the Save Info button
      const allButtons = wrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.text() === 'Save Info' )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

      await updateButton.trigger( 'click' )

      expect( updateOrderDetailsSpy ).toHaveBeenCalled()

      const expectedPayload = {
        backorderRule: 'BACKORDER',
        billing:       {
          address1:    '123 Main St',
          address2:    '456 Main St',
          city:        'Anytown',
          companyName: 'Doe Inc',
          country:     'USA',
          email:       '<EMAIL>',
          firstName:   'Alice',
          lastName:    'Doe',
          paymentType: 'CLIENT',
          phone:       '5555551234',
          poNumber:    'PO12345',
          state:       'CA',
          zip:         '12345'
        },
        clientReference:    'client-ref-1',
        createdByUserId:    '<EMAIL>',
        createdByUsername:  'Birdie Love Portal Test User',
        createdTs:          '2024-09-27T10:39:31.6560298+00:00',
        facilityCode:       'DC1',
        giftMessage:        null,
        groupName:          'Group A',
        holdForRelease:     false,
        id:                 12,
        importErrorCount:   0,
        importErrors:       null,
        importStatus:       'Draft',
        isBusinessOrder:    true,
        isDdp:              false,
        isGift:             true,
        masterRecordId:     null,
        modifiedByUserId:   null,
        modifiedByUsername: null,
        modifiedTs:         null,
        poNumber:           'PO12345',
        shipHandlingFee:    10,
        shipping:           {
          address1:    '123 Oak St',
          address2:    '456 Oak St',
          city:        'Anytown',
          companyName: 'Other Doe Inc',
          country:     'USA',
          email:       '<EMAIL>',
          fax:         null,
          firstName:   'Jane',
          lastName:    'Doe',
          phone:       '555-5678',
          state:       'CA',
          zip:         '12345',
          shipType:    'OWD_USPS_I_PRIORITY',
        },
        shippingMethod: 'OWD_USPS_I_PRIORITY',
        source:         'Shopify'
      }

      // Validate the payload passed to updateDraftOrder
      expect( updateDraftOrder ).toHaveBeenCalled()

      expect( updateDraftOrder ).toHaveBeenCalledWith( 12, expectedPayload )

      // Verify the component emitted the 'update' event exactly once
      expect( wrapper.emitted()).toHaveProperty( 'update' )
      expect( wrapper.emitted( 'update' )?.length ).toBe( 1 )
    })

  })

  describe( 'cancel button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const cancelButton = allButtons.find( b => b.attributes()['data-button'] === 'cancel' )

    it( 'renders the cancel button', () => {
      expect( cancelButton.exists()).toBe( true )
    })

    it( 'fires close event when the cancel button is clicked', async () => {

      expect( cancelButton.exists()).toBe( true )

      await cancelButton.trigger( 'click' )

      expect( wrapper.emitted()).toHaveProperty( 'close' )
      expect( wrapper.emitted( 'close' )?.length ).toBe( 1 )

    })

  })

  describe( 'orderDetailsModel validity', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

    it( 'sets all valid keys in orderDetailsModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

      const orderDetailsModel = wrapper.vm.orderDetailsModel

      // Check that every `valid` property in each field is true
      const allValid = Object.values( orderDetailsModel ).every(
        field => field.valid === true
      )

      expect( allValid ).toBe( true )
      expect( wrapper.vm.canUpdate ).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )
    })

    it( 'sets valid keys on non-mandatory fields in orderDetailsModel to true, canUpdate to true and enable the update button when their values are null', async () => {

      // Set specified non-mandatory fields to null

      const nonMandatoryFields = [ 'poNumber', 'groupName', 'giftMessage' ]

      nonMandatoryFields.forEach(( field ) => {
        wrapper.vm.orderDetailsModel[field].value = null
      })

      await wrapper.vm.$nextTick()

      const orderDetailsModel = wrapper.vm.orderDetailsModel

      // Check only specified keys for `valid` being `true`
      const allNonMandatoryFields = nonMandatoryFields.every(
        field => orderDetailsModel[field].valid === true
      )

      expect( allNonMandatoryFields ).toBe( true )
      expect( wrapper.vm.canUpdate ).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

    })

    it( 'sets backorderRule valid in orderDetailsModel to false, canUpdate to false and disable the update button when the value is null', async () => {

      // Mandatory Radio buttons are not setting valid to false even tho required. This is because they are not nullable.

      // Set specified mandatory fields to null
      const mandatoryFields = [ 'backorderRule' ]

      mandatoryFields.forEach(( field ) => {
        wrapper.vm.orderDetailsModel[field].value = null
      })

      await wrapper.vm.$nextTick()

      const orderDetailsModel = wrapper.vm.orderDetailsModel

      // Check only specified keys for `valid` being `false`
      const allMandatoryFieldsInvalid = mandatoryFields.every(
        field => orderDetailsModel[field].valid === false
      )

      expect( allMandatoryFieldsInvalid ).toBe( true )
      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( updateButton.props().disabled ).toBe( true )

    })

  })

})
