import { ref } from 'vue'
import { updateOrder } from '@/modules/orders/store'
import { mount, shallowMount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'

import Button from '@lib/components/button/Button.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Details from '@/modules/orders/views/live-details/components/Details.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import EditDetailsForm from '@/modules/orders/views/live-details/components/EditDetailsForm.vue'
import LiveOrderDetails from '@/modules/orders/views/live-details/views/LiveOrderDetails.vue'

import type { OrderDetails } from '@/modules/orders/types'

const mockRoute = ref({
  name:     'Live Order Details',
  path:     '/orders/live/details/27292770',
  params:   { orderId: '27292770' },
  fullPath: '/orders/live/details/27292770',
  query:    {},
})

const mockOrder: OrderDetails = {
  id:                           27292770,
  status:                       'On Hold',
  isInternal:                   false,
  isBusinessOrder:              false,
  isGift:                       false,
  giftMessage:                  '',
  isVoid:                       false,
  isShipping:                   false,
  isCalltag:                    false,
  isCod:                        false,
  requestSaturdayDelivery:      false,
  avsOverride:                  false,
  requestSignatureConfirmation: false,
  dutyAndTaxes:                 true,
  isInternational:              false,
  clientReference:              '*********',
  owdReference:                 '36383871',
  type:                         'Direct',
  poNumber:                     'PO67890',
  source:                       '',
  subtotal:                     0.0000,
  discount:                     0.0000,
  salesTax:                     0.0000,
  shipHandlingFee:              0.0000,
  total:                        0.0000,
  owdCoupon:                    '',
  backorderLevel:               0,
  backOrderReference:           null,
  backOrderId:                  null,
  billing:                      {
    firstName:   'Alice',
    lastName:    'Smith',
    companyName: 'Smith LLC',
    address1:    '789 Elm St',
    address2:    '',
    city:        'Othertown',
    state:       'TX',
    zip:         '67890',
    country:     'USA',
    phone:       '555-6789',
    email:       '<EMAIL>',
    paymentType: 'CK',
    fax:         ''
  },
  shipping: {
    firstName:       'Bob',
    lastName:        'Bob',
    address1:        '321 Pine St',
    address2:        '',
    city:            'TX Town',
    state:           'TX',
    zip:             '67890',
    country:         'USA',
    phone:           '555-4321',
    fax:             '',
    email:           '<EMAIL>',
    method:          '2 Day',
    methodReference: null,
    shipType:        null,
    shippingMethod:  '2 Day',
    companyName:     '.'
  },
  actualShipMethod:  null,
  shipPaymentType:   'Prepaid',
  shipAccount:       '',
  insuranceAmount:   0.0000,
  aesItn:            '',
  packageShipment:   null,
  comments:          '',
  notes:             '',
  created:           '2024-09-11T11:22:00',
  released:          null,
  shipped:           null,
  packages:          0,
  predictedShipCost: 0.0000,
  weightLbs:         0.0000,
  trackingNumber:    '',
  shippedLines:      0,
  shippedUnits:      0,
  checkDeposited:    0.0000,
  checkDate:         '1900-01-01T00:00:00',
  shipLocationCode:  'DC1',
  groupName:         'Group B',
  sla:               '1900-01-01T00:00:00',
  shippedTime:       null,
  events:            [
    {
      id:        *********,
      type:      1,
      message:   'Order placed on hold: : Placed on hold per backorder rule: CK form of payment',
      createdAt: '2024-09-11T11:21:59.643',
      creator:   'System'
    },
    {
      id:        *********,
      type:      3,
      message:   'Created',
      createdAt: '2024-09-11T11:21:44.407',
      creator:   'dbo'
    }
  ],
  hadDcHold:    false,
  pickStatus:   0,
  facilityCode: 'DC1',
  packSlip:     null
}

const mockOrderProp: OrderDetails = {
  id:                           ********,
  status:                       'On Hold',
  isInternal:                   false,
  isBusinessOrder:              false,
  isGift:                       false,
  isInternational:              false,
  giftMessage:                  'Happy Birthday',
  isVoid:                       false,
  isShipping:                   false,
  isCalltag:                    false,
  isCod:                        false,
  requestSaturdayDelivery:      false,
  avsOverride:                  false,
  requestSignatureConfirmation: false,
  dutyAndTaxes:                 true,
  clientReference:              '*********345',
  owdReference:                 '36578556',
  type:                         'Direct',
  poNumber:                     '',
  source:                       '',
  subtotal:                     40,
  discount:                     0,
  salesTax:                     0,
  shipHandlingFee:              5,
  total:                        45,
  owdCoupon:                    '',
  backorderLevel:               0,
  backOrderReference:           null,
  backOrderId:                  null,
  billing:                      {
    firstName:   'Alice',
    lastName:    'Smith',
    companyName: 'Smith LLC',
    address1:    '789 Elm St',
    address2:    '',
    city:        'Othertown',
    state:       'TX',
    zip:         '67890',
    country:     'USA',
    phone:       '555-6789',
    email:       '<EMAIL>',
    paymentType: 'CLIENT',
    fax:         ''
  },
  shipping: {
    firstName:       'Bob',
    lastName:        'Bob',
    address1:        '321 Pine St',
    address2:        '',
    city:            'TX Town',
    state:           'TX',
    zip:             '67890',
    country:         'USA',
    phone:           '555-4321',
    fax:             '',
    email:           '<EMAIL>',
    method:          '2 Day',
    methodReference: null,
    companyName:     'Smith Shipping',
  },
  actualShipMethod:  null,
  shipPaymentType:   'Prepaid',
  shipAccount:       '',
  insuranceAmount:   0,
  aesItn:            '',
  packageShipment:   null,
  comments:          '',
  notes:             '',
  created:           '2024-10-15T18:29:00',
  released:          null,
  shipped:           null,
  packages:          0,
  predictedShipCost: 0,
  weightLbs:         0,
  trackingNumber:    '',
  shippedLines:      0,
  shippedUnits:      0,
  checkDeposited:    45,
  checkDate:         '2024-10-15T18:28:50.447',
  shipLocationCode:  'DC1',
  groupName:         'Group B',
  sla:               '1900-01-01T00:00:00',
  shippedTime:       null,
  events:            [
    {
      id:        *********,
      type:      3,
      message:   'Order viewed by New Portal User .',
      createdAt: '2024-10-16T08:18:21.83',
      creator:   'New Portal User'
    }
  ],
  hadDcHold:    false,
  pickStatus:   0,
  facilityCode: 'DC1',
  packSlip:     null
}

const mocks = vi.hoisted(() => ({
  guard: vi.fn(() => true )
}))

vi.mock( 'vue-router', () => ({
  useRoute:  vi.fn(() => mockRoute.value ),
  useRouter: vi.fn(() => ({
    currentRoute: { value: mockRoute.value },
    push:         vi.fn(),
  })),
}))

vi.mock( '@/modules/orders/store', async ( importOriginal ) => {

  const originalModule = await importOriginal() as any

  return {
    ...originalModule, // Spread the original module to keep other exports
    getOrder: vi.fn(() => {
      return new Promise(( resolve ) => {
        setTimeout(() => {
          resolve({
            error:   null,
            payload: mockOrder,
          })
        }, 500 )
      })
    }),
    updateOrder: vi.fn(() => Promise.resolve({
      error:   null,
      status:  201,
      headers: {
        'content-type':      'application/json',
        'date':              'Mon, 21 Oct 2024 10:09:43 GMT',
        'request-context':   'appId=cid-v1:271575a9-4199-40c2-afa4-66b574cb6e2f',
        'transfer-encoding': 'chunked'
      },
      payload: {
        id:                           ********,
        status:                       'On Hold',
        isInternal:                   false,
        isBusinessOrder:              false,
        isGift:                       true,
        giftMessage:                  'Happy Birthday',
        isVoid:                       false,
        isShipping:                   false,
        isCalltag:                    false,
        isCod:                        false,
        requestSaturdayDelivery:      false,
        avsOverride:                  false,
        requestSignatureConfirmation: false,
        dutyAndTaxes:                 true,
        clientReference:              '*********345',
        owdReference:                 '36578556',
        type:                         'Direct',
        poNumber:                     '',
        source:                       '',
        subtotal:                     40,
        discount:                     0,
        salesTax:                     0,
        shipHandlingFee:              5,
        total:                        45,
        owdCoupon:                    '',
        backorderLevel:               0,
        backOrderReference:           null,
        backOrderId:                  null,
        billing:                      {
          firstName:   'Alice',
          lastName:    'Smith',
          companyName: 'Smith LLC',
          address1:    '789 Elm St',
          address2:    '',
          city:        'Othertown',
          state:       'TX',
          zip:         '67890',
          country:     'USA',
          phone:       '555-6789',
          email:       '<EMAIL>',
          paymentType: 'CLIENT',
          fax:         ''
        },
        shipping: {
          firstName:       'Bob',
          lastName:        'Bob',
          address1:        '321 Pine St',
          address2:        '',
          city:            'TX Town',
          state:           'TX',
          zip:             '67890',
          country:         'USA',
          phone:           '555-4321',
          fax:             '',
          email:           '<EMAIL>',
          method:          '2 Day',
          methodReference: null,
          companyName:     'Smith Shipping'
        },
        actualShipMethod:  null,
        shipPaymentType:   'Prepaid',
        shipAccount:       '',
        insuranceAmount:   0,
        aesItn:            '',
        packageShipment:   null,
        comments:          '',
        notes:             '',
        created:           '2024-10-15T18:29:00',
        released:          null,
        shipped:           null,
        packages:          0,
        predictedShipCost: 0,
        weightLbs:         0,
        trackingNumber:    '',
        shippedLines:      0,
        shippedUnits:      0,
        checkDeposited:    45,
        checkDate:         '2024-10-15T18:28:50.447',
        shipLocationCode:  'DC1',
        groupName:         'Group B',
        sla:               '1900-01-01T00:00:00',
        shippedTime:       null,
        events:            [
          {
            id:        *********,
            type:      3,
            message:   'Created',
            createdAt: '2024-10-15T13:28:49.42',
            creator:   'dbo'
          }
        ],
        hadDcHold:    false,
        pickStatus:   0,
        facilityCode: 'DC1',
        packSlip:     null
      }
    })),
    internalTaxOptions: [
      {
        id:   1,
        name: 'Delivery Dispatch Import',
      },
      {
        id:   0,
        name: 'Delivered Duty Unpaid',
      },
    ],
  }

})

vi.mock( '@/store', async ( importOriginal ) => {
  const original = await importOriginal() as Record<any, any>

  return {
    ...original,
    shippingMethodsList: ref( [
      { id: 'COM_OWD_FLATRATE_2DA', name: '2 Day' },
      { id: 'COM_OWD_FLATRATE_BPMP', name: 'Bound Printed Matter Parcel' },
      { id: 'CONNECTSHIP_DHL.DHL.WPX', name: 'DHL Express Worldwide nondoc' },
      { id: 'COM_OWD_FLATRATE_ECONOMY', name: 'Economy' },
      { id: 'FDX.2DA', name: 'FedEx 2Day' },
      { id: 'FDX.ECO', name: 'FedEx Express Saver' },
      { id: 'FDX.GND', name: 'FedEx Ground' },
      { id: 'FDX.HD', name: 'FedEx Home Delivery' },
      { id: 'TANDATA_FEDEXFSMS.FEDEX.IECO', name: 'FedEx International Economy' },
      { id: 'TANDATA_FEDEXFSMS.FEDEX.IPRI', name: 'FedEx International Priority' },
      { id: 'FDX.PRI', name: 'FedEx Priority Overnight' },
      { id: 'TANDATA_FEDEXFSMS.FEDEX.SP_PS', name: 'FedEx SmartPost Parcel Select' },
      { id: 'TANDATA_FEDEXFSMS.FEDEX.SP_STD', name: 'FedEx SmartPost Standard Mail' },
      { id: 'FDX.STD', name: 'FedEx Standard Overnight' },
      { id: 'COM_OWD_FLATRATE_GROUND', name: 'Ground' },
      { id: 'PS_ALF.EHUB.USPS_GROUND_ADVANTAGE', name: 'Ground Advantage' },
      { id: 'COM_OWD_FLATRATE_INTL_ECONOMY', name: 'International Economy' },
      { id: 'COM_OWD_FLATRATE_INTL_EXPD', name: 'International Expedited' },
      { id: 'COM_OWD_FLATRATE_INTL_PRIDDP', name: 'International Priority DDP' },
      { id: 'COM_OWD_FLATRATE_INTL_PRIDDU', name: 'International Priority DDU' },
      { id: 'COM_OWD_FLATRATE_INTL_STND', name: 'International Standard' },
      { id: 'TANDATA_LTL.LTL.LTL', name: 'LTL' },
      { id: 'COM_OWD_FLATRATE_MM', name: 'Media Mail' },
      { id: 'COM_OWD_FLATRATE_NDA', name: 'Overnight' },
      { id: 'PS_ALF.EHUB.USPS_PRIORITY', name: 'Priority Mail' },
      { id: 'COM_OWD_FLATRATE_STANDARD_GROUND', name: 'Standard Priority' },
      { id: 'UPS.2DA', name: 'UPS 2nd Day Air' },
      { id: 'UPS.2AM', name: 'UPS 2nd Day Air A.M.' },
      { id: 'UPS.3DA', name: 'UPS 3 Day Select' },
      { id: 'UPS.WEPD', name: 'UPS Expedited' },
      { id: 'UPS.WEXP', name: 'UPS Express' },
      { id: 'TANDATA_UPS.UPS.WEXPPLS', name: 'UPS Express Plus' },
      { id: 'TANDATA_UPS.UPS.WEXPSVR', name: 'UPS Express Saver' },
      { id: 'UPS.GND', name: 'UPS Ground' },
      { id: 'UPS.NDA', name: 'UPS Next Day Air' },
      { id: 'UPS.NAM', name: 'UPS Next Day Air A.M.' },
      { id: 'UPS.NDS', name: 'UPS Next Day Air Saver' },
      { id: 'UPS.STDCAMX', name: 'UPS Standard Canada' },
      { id: 'UPS.SPPS', name: 'UPS SurePost 1 lb or Greater' },
      { id: 'UPS.SPSTD', name: 'UPS SurePost Less than 1 lb' },
      { id: 'TANDATA_USPS.USPS.BPM', name: 'USPS Bnd Prt Mtr Single Piece' },
      { id: 'TANDATA_USPS.USPS.BPMSP', name: 'USPS Bound Printed Matter' },
      { id: 'OWD.1ST.LETTER', name: 'USPS First-Class Mail' },
      { id: 'OWD_USPS_I_FIRST', name: 'USPS First-Class Mail International' },
      { id: 'OWD.4TH.BOOK', name: 'USPS Media Mail Single-Piece' },
      { id: 'OWD.4TH.PARCEL', name: 'USPS Parcel Select Nonpresort' },
      { id: 'OWD.1ST.PRIORITY', name: 'USPS Priority Mail' },
      { id: 'TANDATA_USPS.USPS.PRIORITY_CUBIC', name: 'USPS Priority Mail (Cubic)' },
      { id: 'POS.EXP', name: 'USPS Priority Mail Express' },
      { id: 'OWD_USPS_I_EXP_DMND', name: 'USPS Priority Mail Express International' },
      { id: 'OWD_USPS_I_PRIORITY', name: 'USPS Priority Mail International' }
    ] ),
  }
})

vi.mock( '@/plugins/guard', async ( importOriginal ) => {

  const original = await importOriginal() as Record<any, any>

  return {
    ...original,
    guard: mocks.guard
  }

})

describe( 'live order details - [ view ]', () => {

  const wrapper = shallowMount( LiveOrderDetails, {
    stubs: {
      Loader,
      Details
    }
  })

  it( 'orderId param is correct', () => {
    expect( wrapper.vm.route.params.orderId ).toBe( '27292770' )
  })

  it( 'view to be in pending state', () => {
    wrapper.vm.pending = true
    wrapper.vm.$nextTick()

    expect( wrapper.vm.pending ).toBe( true )
  })

  it( 'loader component exists when pending', () => {
    expect( wrapper.findComponent( Loader ).exists()).toBe( true )
  })

  it( 'order properties to be correct', async () => {

    await wrapper.vm.getOrderDetails( Number.parseInt( String( wrapper.vm.route.params.orderId )))

    expect( wrapper.vm.order.type ).toBe( 'Direct' )
    expect( wrapper.vm.order.clientReference ).toBe( '*********' )
    expect( wrapper.vm.order.billing.firstName ).toBe( 'Alice' )
    expect( wrapper.vm.order.shipping.firstName ).toBe( 'Bob' )

  })

  it( 'view to not be in pending state', () => {
    expect( wrapper.vm.pending ).toBe( false )
    expect( wrapper.text()).toContain( 'Live Order: [*********]' )
  })

  it( 'openActions is true and the sidebar is open', async () => {

    // Find the details component and emit the openActions event
    const detailsList = wrapper.findAllComponents( Details )
    const detailsWrapper = detailsList.find( d => d.attributes()['data-component-desktop'] = 'details' )

    detailsWrapper.vm.$emit( 'openActions' )

    expect( wrapper.vm.openActions ).toBe( true )
    await wrapper.vm.$nextTick()

    // Find the sidebar component and check if it is open

    const sidebarsList = wrapper.findAllComponents( Sidebar )
    const sidebarWrapper = sidebarsList.find( sb => sb.props().open )

    expect( sidebarWrapper.exists()).toBe( true )

  })

  describe( 'actions buttons in the sidebar are correct', () => {

    it( 'actions are correct when order status is "On Hold"', async () => {

      // If the order status is 'On Hold' then the sidebar should have 2 buttons

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 2 )

      // The first button should be Void Order action.

      const voidButton = actionsButtons.find( b => b.text() === 'Void Order' )

      expect( voidButton.exists()).toBe( true )

      // The second button should be Remove Hold - Backorder All.

      const removeHoldButton = actionsButtons.find( b => b.text() === 'Remove Hold - Backorder All' )

      expect( removeHoldButton.exists()).toBe( true )

    })

    it( 'actions are correct when order status is "Backorder (Active) and order can be partial"', async () => {

      // If the order status is 'Backorder (Active)'
      // and canBePartial is true then the sidebar should have 3 buttons

      wrapper.vm.order.status = 'Backorder (Active)'
      wrapper.vm.canBePartial = true

      await wrapper.vm.$nextTick()

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 3 )

      // The first button should be Void Order action.

      const voidButton = actionsButtons.find( b => b.text() === 'Void Order' )

      expect( voidButton.exists()).toBe( true )

      // The second button should be Set on hold action.

      const setOnHoldButton = actionsButtons.find( b => b.text() === 'Set On Hold' )

      expect( setOnHoldButton.exists()).toBe( true )

      // The third button should be Release Order - Ship Available Items.

      const releaseButton = actionsButtons.find( b => b.text() === 'Release Order - Ship Available Items' )

      expect( releaseButton.exists()).toBe( true )

    })

    it( 'actions are correct when order status is "Backorder (Active) and order can\'t be partial', async () => {

      // If the order status is 'Backorder (Active)'
      // and canBePartial is false then the sidebar should have 2 buttons

      wrapper.vm.canBePartial = false

      await wrapper.vm.$nextTick()

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 2 )

      // The first button should be Void Order action.

      const voidButton = actionsButtons.find( b => b.text() === 'Void Order' )

      expect( voidButton.exists()).toBe( true )

      // The second button should be Set on hold action.

      const setOnHoldButton = actionsButtons.find( b => b.text() === 'Set On Hold' )

      expect( setOnHoldButton.exists()).toBe( true )

    })

    it( 'actions are correct when order status is "At Warehouse"', async () => {

      // If the order status is 'At Warehouse' then the sidebar should have 1 button

      wrapper.vm.order.status = 'At Warehouse'

      await wrapper.vm.$nextTick()

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 1 )

      // The button should be Unpost Order action.

      const unpostButton = actionsButtons.find( b => b.text() === 'Unpost Order' )

      expect( unpostButton.exists()).toBe( true )

    })

    it ( 'there should be no actions if the order status is "Void"', async () => {

      wrapper.vm.order.status = 'Void'

      await wrapper.vm.$nextTick()

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 0 )

    })

    it ( 'there should be no actions if the order status is "Shipped"', async () => {

      wrapper.vm.order.status = 'Shipped'

      await wrapper.vm.$nextTick()

      const buttonsList = wrapper.findAllComponents( Button )
      const actionsButtons = buttonsList.filter( b => b.attributes()['data-button-desktop'] === 'actions' )

      expect( actionsButtons.length ).toBe( 0 )

    })

  })

})

describe( 'live order details - [ component::details ]', () => {

  const wrapper = shallowMount( Details, {
    props: { order: { ...mockOrder, status: 'On Hold' } }
  })

  it( 'order prop is correct', () => {
    expect( wrapper.vm.order.id ).toBe( 27292770 )
  })

  describe( 'actions Button', async () => {

    const buttons = wrapper.findAllComponents( Button )
    const actionsButton = buttons.find( b => b.attributes()['data-button'] === 'actions' )

    it( 'actions button exists', async () => {

      expect( wrapper.vm.noOptions ).toBe( false )
      expect( actionsButton.exists()).toBe( true )

    })

    it( 'actions button emits openActions event', () => {

      actionsButton.vm.$emit( 'click' )
      expect( wrapper.emitted().openActions ).toBeTruthy()

    })

    it( 'button is not rendered when has no options', async () => {

      wrapper.props().order.status = 'Shipped'

      await wrapper.vm.$nextTick()

      expect( wrapper.findAllComponents( Button ).find( b => b.attributes()['data-button'] === 'actions' )).toBe( undefined )

    })

  })

})

describe( 'live order details - [ component::EditDetailsForm ]', () => {

  // Create a div to serve as the teleport target
  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const wrapper = mount( EditDetailsForm, {
    attachTo: document.body,
    props:    {
      order:   mockOrderProp,
      pending: false
    }
  })

  it( 'renders the edit details component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'details form', () => {

    it( 'sets the "Billing details are same as shipping" checkbox to false by default', () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allCheckboxes = shippingSection.findAllComponents( Checkbox )

      expect( allCheckboxes[0].text()).toBe( 'Billing details are same as shipping' )
      expect( allCheckboxes[0].props().modelValue ).toBe( false )

    })

    it( 'renders all the fields with the correct values in the Billing details section', () => {

      const billingSection = wrapper.find( '[data-section="billing"]' )

      const allInputs = billingSection.findAllComponents( InputField )
      const allSelects = billingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Alice' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Smith' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Smith LLC' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '789 Elm St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'TX' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'Othertown' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '67890' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '555-6789' )

    })

    it( 'renders all the fields with the correct values in the Shipping details section', () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allInputs = shippingSection.findAllComponents( InputField )
      const allSelects = shippingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Bob' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Bob' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Smith Shipping' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '321 Pine St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'TX' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'TX Town' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '67890' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '555-4321' )

    })

    it( 'renders all the fields with the correct values in the Delivery details section', () => {

      const deliverySection = wrapper.find( '[data-section="delivery"]' )

      const allInputs = deliverySection.findAllComponents( InputField )
      const allSelects = deliverySection.findAllComponents( '[data-input-type="select"]' )
      const allCheckboxes = deliverySection.findAllComponents( Checkbox )

      // Shipping Method
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().modelValue ).toBe( 'COM_OWD_FLATRATE_2DA' )
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Shipping Method' ).props().nullable ).toBe( false )

      // Carrier Billing
      expect( allSelects.find( s => s.props().label === 'Carrier Billing' ).props().modelValue ).toBe( 'Prepaid' )
      expect( allSelects.find( s => s.props().label === 'Carrier Billing' ).props().required ).toBe( false )
      expect( allSelects.find( s => s.props().label === 'Carrier Billing' ).props().nullable ).toBe( true )

      // Declared Value
      expect( allInputs.find( i => i.props().label === 'Declared Value' ).props().modelValue ).toBe( 0 )
      expect( allInputs.find( i => i.props().label === 'Declared Value' ).props().required ).toBe( false )

      // AES ITN
      expect( allInputs.find( i => i.props().label === 'AES ITN' ).props().modelValue ).toBe( '' )
      expect( allInputs.find( i => i.props().label === 'AES ITN' ).props().required ).toBe( false )

      // International Tax And Duty
      expect( allSelects.find( s => s.props().label === 'International Tax And Duty' ).props().modelValue ).toBe( 1 )
      expect( allSelects.find( s => s.props().label === 'International Tax And Duty' ).props().nullable ).toBe( false )
      expect( allSelects.find( s => s.props().label === 'International Tax And Duty' ).props().required ).toBe( true )

      // Request Saturday Delivery
      expect( allCheckboxes.find( c => c.props().label === 'Request Saturday Delivery' ).props().modelValue ).toBe( false )

      // Request Signature Confirmation
      expect( allCheckboxes.find( c => c.props().label === 'Request Signature Confirmation' ).props().modelValue ).toBe( false )

      // Bypass AVS
      expect( allCheckboxes.find( c => c.props().label === 'Bypass AVS' ).props().modelValue ).toBe( false )

      // Use Gift Invoice
      expect( allCheckboxes.find( c => c.props().label === 'Use Gift Invoice' ).props().modelValue ).toBe( false )

    })

    it( 'shows gift message text box when Use Gift Invoice is checked and hide it if its not checked', async () => {

      const deliverySection = wrapper.find( '[data-section="delivery"]' )
      const allCheckboxes = deliverySection.findAllComponents( Checkbox )

      let useGiftCheckbox =  allCheckboxes.find( c => c.props().label === 'Use Gift Invoice' )
      let allTextBoxes = deliverySection.findAllComponents( '[data-input-type="textarea"]' )

      expect( allTextBoxes.find( t => t.props().label === 'Optional Gift Message (up to size of box)' )).toBeUndefined()

      // Use Gift Invoice

      wrapper.vm.detailsModel.isGift.value = true

      await wrapper.vm.$nextTick()

      useGiftCheckbox = allCheckboxes.find( c => c.props().label === 'Use Gift Invoice' )
      expect( useGiftCheckbox.props().modelValue ).toBe( true )

      allTextBoxes = deliverySection.findAllComponents( '[data-input-type="textarea"]' )
      expect( allTextBoxes.find( t => t.props().label === 'Optional Gift Message (up to size of box)' ).exists()).toBe( true )

    })

    it( 'copies the shipping details to billing details when "Billing details are same as shipping" checkbox to set to true', async () => {

      const shippingSection = wrapper.find( '[data-section="shipping"]' )

      const allCheckboxes = shippingSection.findAllComponents( Checkbox )

      const sameDetailsCheckbox = allCheckboxes[0]

      // The label is the clickable element in the checkbox
      const sameDetailsCheckboxLabel = allCheckboxes[0].find( 'label' )

      // Click the label to copy the shipping info into billing
      await sameDetailsCheckboxLabel.trigger( 'click' )
      await wrapper.vm.$nextTick()

      // Confirm that it's the correct checkbox
      expect( sameDetailsCheckbox.text()).toBe( 'Billing details are same as shipping' )

      // Confirm that the value is changed
      expect( sameDetailsCheckbox.props().modelValue ).toBe( true )

      const billingSection = wrapper.find( '[data-section="billing"]' )

      const allInputs = billingSection.findAllComponents( InputField )
      const allSelects = billingSection.findAllComponents( '[data-input-type="select"]' )

      // First Name *
      expect( allInputs.find( i => i.props().label === 'First Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'First Name' ).props().modelValue ).toBe( 'Bob' )

      // Last Name *
      expect( allInputs.find( i => i.props().label === 'Last Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Last Name' ).props().modelValue ).toBe( 'Bob' )

      // Company name
      expect( allInputs.find( i => i.props().label === 'Company Name' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Company Name' ).props().modelValue ).toBe( 'Smith Shipping' )

      // Address Line 1 *
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 1' ).props().modelValue ).toBe( '321 Pine St' )

      // Address Line 2
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Address Line 2' ).props().modelValue ).toBe( '' )

      // Country *
      expect( allSelects.find( s => s.props().label === 'Country' ).exists()).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().nullable ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().required ).toBe( true )
      expect( allSelects.find( s => s.props().label === 'Country' ).props().modelValue ).toBe( 'USA' )

      // State *
      expect( allInputs.find( i => i.props().label === 'State' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'State' ).props().modelValue ).toBe( 'TX' )

      // City *
      expect( allInputs.find( i => i.props().label === 'City' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'City' ).props().modelValue ).toBe( 'TX Town' )

      // Postal Code *
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().required ).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Postal Code' ).props().modelValue ).toBe( '67890' )

      // Email
      expect( allInputs.find( i => i.props().label === 'Email' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Email' ).props().modelValue ).toBe( '<EMAIL>' )

      // Phone
      expect( allInputs.find( i => i.props().label === 'Phone' ).exists()).toBe( true )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().required ).toBe( false )
      expect( allInputs.find( i => i.props().label === 'Phone' ).props().modelValue ).toBe( '555-4321' )

    })

  })

  describe( 'update order(Save Changes) button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

    it( 'sets Save Changes button disabled to false and canUpdate to true if all the details are correct', () => {

      expect( wrapper.vm.canUpdate ).toBe( true )
      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

    })

    it( 'sets Save Changes button disabled to true and sets canUpdate to false if firstName is not valid', async () => {

      // set one field to empty
      wrapper.vm.billingModel.firstName.value = ''

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.billingModel.firstName.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

    })

    it( 'sets Save Changes button disabled to true and sets canUpdate to false if lastName is not valid', async () => {

      // set one field to empty
      wrapper.vm.billingModel.lastName.value = ''

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.billingModel.lastName.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

      wrapper.vm.billingModel.lastName.value = 'Bob'
    })

    it( 'sets Save Changes button disabled to true and sets canUpdate to false if address1 is not valid', async () => {

      // set one field to empty
      wrapper.vm.billingModel.address1.value = ''

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.billingModel.address1.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

      wrapper.vm.billingModel.address1.value = '321 Pine St'

    })

    it( 'sets Save Changes button disabled to true and sets canUpdate to false if country is not valid', async () => {

      // set one field to empty
      wrapper.vm.billingModel.country.value = ''

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.canUpdate ).toBe( false )
      expect( wrapper.vm.billingModel.country.valid ).toBe( false )

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( true )

      wrapper.vm.billingModel.country.value = 'USA'

    })

    it( 'calls updateOrderDetails and updateOrder when the Save Changes button is clicked', async () => {

      // Spy on the updateOrderDetails method
      const updateOrderDetailsSpy = vi.spyOn( wrapper.vm, 'updateOrderDetails' )

      // Ensure that all required fields are valid for the button to be enabled
      wrapper.vm.billingModel.firstName.value = 'John'

      await wrapper.vm.$nextTick()

      expect( updateButton.exists()).toBe( true )
      expect( updateButton.props().disabled ).toBe( false )

      // Simulate clicking the button
      await updateButton.trigger( 'click' )

      const expectedPayload = {
        aesItn:      '',
        avsOverride: false,
        billing:     {
          address1:    '321 Pine St',
          address2:    '',
          city:        'TX Town',
          companyName: 'Smith Shipping',
          country:     'USA',
          email:       '<EMAIL>',
          fax:         '',
          firstName:   'Bob',
          lastName:    'Bob',
          paymentType: 'CLIENT',
          phone:       '555-4321',
          state:       'TX',
          zip:         '67890'
        },
        carrier:                      '2 Day',
        carrierReferenceNumber:       'COM_OWD_FLATRATE_2DA',
        declaredValue:                0,
        dutyAndTaxes:                 true,
        giftMessage:                  'Happy Birthday',
        isGift:                       true,
        requestSaturdayDelivery:      false,
        requestSignatureConfirmation: false,
        shipAccount:                  '',
        shipPaymentType:              'Prepaid',
        shipping:                     {
          address1:        '321 Pine St',
          address2:        '',
          city:            'TX Town',
          companyName:     'Smith Shipping',
          country:         'USA',
          email:           '<EMAIL>',
          fax:             '',
          firstName:       'Bob',
          lastName:        'Bob',
          method:          '2 Day',
          methodReference: null,
          phone:           '555-4321',
          state:           'TX',
          zip:             '67890'
        }
      }

      // Check if the updateOrderDetails method was called
      expect( updateOrderDetailsSpy ).toHaveBeenCalled()

      // Check if the updateOrder method from the store was called
      expect( updateOrder ).toHaveBeenCalled()
      expect( updateOrder ).toHaveBeenCalledWith( ********, expectedPayload )

      // Verify the component emitted the 'update' event exactly once
      expect( wrapper.emitted()).toHaveProperty( 'update' )
      expect( wrapper.emitted( 'update' )?.length ).toBe( 1 )

    })

    describe( 'cancel button', () => {

      const allButtons = wrapper.findAllComponents( Button )
      const cancelButton = allButtons.find( b => b.attributes()['data-button'] === 'cancel' )

      it( 'renders the cancel button', () => {
        expect( cancelButton.exists()).toBe( true )
      })

      it( 'fires close event when the cancel button is clicked', async () => {

        expect( cancelButton.exists()).toBe( true )

        await cancelButton.trigger( 'click' )

        expect( wrapper.emitted()).toHaveProperty( 'close' )
        expect( wrapper.emitted( 'close' )?.length ).toBe( 1 )

      })

    })

  })

  describe( 'models validity', () => {

    describe( 'billingModel validity', () => {

      const allButtons = wrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

      it( 'sets all valid keys in billingModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

        const billingModel = wrapper.vm.billingModel

        // Check that every `valid` property in each field is true
        const allValid = Object.values( billingModel ).every(
          field => field.valid === true
        )
        expect( wrapper.vm.canUpdate ).toBe( true )
        expect( allValid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )

      })

      it( 'sets valid keys on non-mandatory fields in billingModel to true, canUpdate to true and enable the update button when their values are null', async () => {

        // Define non-mandatory fields
        const nonMandatoryFields = [ 'companyName', 'address2', 'email', 'phone' ]

        nonMandatoryFields.forEach(( field ) => {
          wrapper.vm.billingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const billingModel = wrapper.vm.billingModel

        // Check only specified keys for `valid` being `true`
        const allNonMandatoryFields = nonMandatoryFields.every(
          field => billingModel[field]?.valid === true
        )

        expect( wrapper.vm.canUpdate ).toBe( true )
        expect( allNonMandatoryFields ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )
      })

      it( 'sets valid keys on mandatory fields in billingModel to false, canUpdate to false and disable the update button when their values are null', async () => {

        // Set specified mandatory fields to null
        const mandatoryFields = [ 'firstName', 'lastName', 'address1', 'country', 'state', 'city', 'zip' ]

        mandatoryFields.forEach(( field ) => {
          wrapper.vm.billingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const shippingModel = wrapper.vm.billingModel

        // Check only specified keys for `valid` being `false`
        const allMandatoryFieldsInvalid = mandatoryFields.every(
          field => shippingModel[field].valid === false
        )
        expect( wrapper.vm.canUpdate ).toBe( false )
        expect( allMandatoryFieldsInvalid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( true )

      })

    })

    describe( 'shippingModel validity', () => {

      const newWrapper = mount( EditDetailsForm, {
        attachTo: document.body,
        props:    {
          order:   mockOrderProp,
          pending: false
        }
      })

      const allButtons = newWrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

      it( 'sets all valid keys in shippingModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

        const shippingModel = newWrapper.vm.shippingModel

        // Check that every `valid` property in each field is true
        const allValid = Object.values( shippingModel ).every(
          field => field.valid === true
        )

        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allValid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )
      })

      it( 'sets valid keys on non-mandatory fields in shippingModel to true, canUpdate to true and enable the update button when their values are null', async () => {

        // Define non-mandatory fields
        const nonMandatoryFields = [ 'companyName', 'address2', 'email', 'phone' ]

        nonMandatoryFields.forEach(( field ) => {
          newWrapper.vm.shippingModel[field].value = null
        })

        await newWrapper.vm.$nextTick()

        const shippingModel = newWrapper.vm.shippingModel

        // Check only specified keys for `valid` being `true`
        const allNonMandatoryFields = nonMandatoryFields.every(
          field => shippingModel[field]?.valid === true
        )

        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allNonMandatoryFields ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )
      })

      it( 'sets valid keys on mandatory fields in shippingModel to false, canUpdate to false, and disable the update button when their values are null', async () => {

        // Set specified mandatory fields to null
        const mandatoryFields = [ 'firstName', 'lastName', 'address1', 'country', 'state', 'city', 'zip' ]

        mandatoryFields.forEach(( field ) => {
          newWrapper.vm.shippingModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const shippingModel = newWrapper.vm.shippingModel

        // Check only specified keys for `valid` being `false`
        const allMandatoryFieldsInvalid = mandatoryFields.every(
          field => shippingModel[field].valid === false
        )
        expect( newWrapper.vm.canUpdate ).toBe( false )
        expect( allMandatoryFieldsInvalid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( true )
      })

    })

    describe( 'detailsModel validity', () => {

      const newWrapper = mount( EditDetailsForm, {
        attachTo: document.body,
        props:    {
          order:   mockOrderProp,
          pending: false
        }
      })

      const allButtons = newWrapper.findAllComponents( Button )
      const updateButton = allButtons.find( b => b.attributes()['data-button'] === 'update' )

      it( 'sets all valid keys in detailsModel to true, canUpdate to true and enable the update button when all mandatory fields have value', () => {

        const detailsModel = newWrapper.vm.detailsModel

        // Check that every `valid` property in each field is true
        const allValid = Object.values( detailsModel ).every(
          field => field.valid === true
        )
        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allValid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )
      })

      it( 'sets valid keys on non-mandatory fields in detailsModel to true, canUpdate to true and enable the update button when their values are null', async () => {

        // Define non-mandatory fields
        const nonMandatoryFields = [ 'carrier', 'aesItn', 'requestSignatureConfirmation', 'requestSaturdayDelivery', 'avsOverride', 'isGift', 'shipPaymentType', 'declaredValue' ]

        nonMandatoryFields.forEach(( field ) => {
          newWrapper.vm.detailsModel[field].value = null
        })

        await newWrapper.vm.$nextTick()

        const detailsModel = newWrapper.vm.detailsModel

        // Check only specified keys for `valid` being `true`
        const allNonMandatoryFields = nonMandatoryFields.every(
          field => detailsModel[field]?.valid === true
        )

        expect( newWrapper.vm.canUpdate ).toBe( true )
        expect( allNonMandatoryFields ).toBe( true )
        expect( updateButton.props().disabled ).toBe( false )
      })

      it( 'sets valid keys on mandatory fields in detailsModel to false, canUpdate to false and disable the update button when their values are null', async () => {

        // Set specified mandatory fields to null
        // Duty and taxes is not a nullable fields, valid is always true
        const mandatoryFields = [ 'carrierReferenceNumber' ]

        mandatoryFields.forEach(( field ) => {
          newWrapper.vm.detailsModel[field].value = null
        })

        await wrapper.vm.$nextTick()

        const detailsModel = newWrapper.vm.detailsModel

        // Check only specified keys for `valid` being `false`
        const allMandatoryFieldsInvalid = mandatoryFields.every(
          field => detailsModel[field].valid === false
        )
        expect( newWrapper.vm.canUpdate ).toBe( false )
        expect( allMandatoryFieldsInvalid ).toBe( true )
        expect( updateButton.props().disabled ).toBe( true )
      })

    })

  })

})
