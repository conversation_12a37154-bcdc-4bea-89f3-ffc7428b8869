import { mount, shallowMount } from '@vue/test-utils'
import { defaultFacility, facilitiesList } from '@/store'
import { createDraftOrder, createLiveOrder } from '@/modules/orders/store'
import { beforeEach, describe, expect, it, vi } from 'vitest'

import {
  activeStep,
  billing,
  clientStepValid,
  details,
  inventoryItems,
  isDraftOrder,
  isSameInfo,
  orderStepValid,
  productsStepValid,
  selectedItems,
  shipping,
  shippingGroups,
  shippingStepValid
} from '@/modules/orders/views/create-order/store.ts'

import Row from '@lib/components/table/Row.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Radio from '@lib/components/inputs/Radio.vue'
import Order from '@/modules/orders/views/create-order/components/Order.vue'
import Button from '@lib/components/button/Button.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Client from '@/modules/orders/views/create-order/components/Client.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Summary from '@/modules/orders/views/create-order/components/Summary.vue'
import Products from '@/modules/orders/views/create-order/components/Products.vue'
import Shipping from '@/modules/orders/views/create-order/components/Shipping.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import CreateOrder from '@/modules/orders/views/create-order/views/CreateOrder.vue'
import CreateOrderFooter from '@/modules/orders/views/create-order/components/CreateOrderFooter.vue'
import CreateOrderInventory from '@/modules/orders/views/create-order/components/CreateOrderInventory.vue'
import CreateOrderInventoryProduct from '@/modules/orders/views/create-order/components/CreateOrderInventoryProduct.vue'

vi.mock( '@/store', async ( importOriginal ) => {

  const actual = await importOriginal() as any

  actual.defaultCountry.value = 'USA'
  actual.defaultFacility.value = 'DC1'
  actual.facilitiesList.value = [
    {
      id:   'DC1',
      name: 'Midwest - Mobridge, SD'
    }
  ]

  return {
    ...actual
  }

})

vi.mock( '@/views/create-order/store.ts', async ( importOriginal ) => {

  const actual = await importOriginal() as any
  actual.hasFacility.value = false

  return {
    ...actual
  }

})

vi.mock( '@/modules/orders/store', async ( importOriginal ) => {

  const actual = await importOriginal() as any

  return {
    ...actual,
    createDraftOrder: vi.fn(() =>
      Promise.resolve({
        error:   null,
        status:  200,
        headers: {
          'content-encoding':  'gzip',
          'content-type':      'application/json; charset=utf-8',
          'date':              'Tue, 22 Oct 2024 13:41:54 GMT',
          'request-context':   'appId=cid-v1:271575a9-4199-40c2-afa4-66b574cb6e2f',
          'transfer-encoding': 'chunked',
          'vary':              'Accept-Encoding'
        },
        payload: {}
      })
    ),
    createLiveOrder: vi.fn(() =>
      Promise.resolve({
        error:   null,
        status:  201,
        headers: {
          'content-type':      'application/json',
          'date':              'Tue, 22 Oct 2024 13:56:54 GMT',
          'request-context':   'appId=cid-v1:271575a9-4199-40c2-afa4-66b574cb6e2f',
          'transfer-encoding': 'chunked'
        },
        payload: {}
      })
    ),
  }

})

describe( 'create order - [component:createOrderInventory]', () => {

  // Create a div to serve as the teleport target
  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const newWrapper = mount( CreateOrderInventory, {
    attachTo: document.body,
    props:    {
      params: {
        page:     1,
        pageSize: 25
      },
      total:    591,
      maxPages: 24,
      pending:  false
    }

  })

  it( 'renders the createOrderInventory component', async () => {
    expect( newWrapper.exists()).toBe( true )
  })

  it( 'has no line items by default', () => {
    expect( inventoryItems.value.length ).toBe( 0 )
  })

  it( 'displays [Midwest - Mobridge, SD] as a facility name for DC1', async () => {

    details.value.facilityRule.value = 'DC1'

    await newWrapper.vm.$nextTick()

    expect( newWrapper.find( '[data-facility="facility"]' ).text()).toBe( '[Midwest - Mobridge, SD]' )

  })

  it( 'hides the table rows if no items are available', () => {
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )
    expect( tableRows.length ).toBe( 0 )
  })

  it( 'disables the mobile confirm(next step) button if there are no items added', () => {

    const allButtons = newWrapper.findAllComponents( Button )
    const confirmButton = allButtons.find( b => b.text() === 'Confirm Products' )

    expect( productsStepValid.value ).toBe( false )
    expect( confirmButton.props().disabled ).toBe( true )

  })

  it( 'renders locked inventory item row in the table when the item is not available in the correct facility', async () => {

    inventoryItems.value = [
      {
        id:                  631469,
        sku:                 'RideOnDVD',
        title:               'Ride On DVD',
        availableQuantity:   5,
        availableByFacility: [
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 5,
            lots:           null
          }
        ],
        backOrdered:   0,
        price:         24.95,
        supplier:      '',
        kitComponents: null,
        locked:        true
      }
    ]

    // Wait for the UI update
    await newWrapper.vm.$nextTick()
    // Find all table rows
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    const lockedRow = tableRows.find( tr => tr.props().item.availableByFacility[0].facilityCode === 'DC6' )
    const allIcons = lockedRow.findAllComponents( Icon )

    const notAvailableCell = lockedRow.find( '[data-availability-mobile="not-available"]' )

    const desktopLockedIcon = allIcons.find( i => i.attributes()['data-icon-desktop'] === 'locked' )
    const mobileLockedIcon = allIcons.find( i => i.attributes()['data-icon-mobile'] === 'locked' )

    expect( notAvailableCell.text()).toBe( 'Not Available' )
    expect( desktopLockedIcon.exists()).toBe( true )
    expect( mobileLockedIcon.exists()).toBe( true )
  })

  it( 'renders available inventory item row in the table when the item is available in the correct facility', async () => {

    inventoryItems.value = [
      {
        id:                  631469,
        sku:                 'RideOnDVD',
        title:               'Ride On DVD',
        availableQuantity:   5,
        availableByFacility: [
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 5,
            lots:           null
          }
        ],
        backOrdered:   0,
        price:         24.95,
        supplier:      '',
        kitComponents: null,
        locked:        true
      }
    ]

    // Change the facility to be the one where the item is available
    details.value.facilityRule.value = 'DC6'

    // Wait for the update
    await newWrapper.vm.$nextTick()

    // Find all table rows
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    const lockedRow = tableRows.find( tr => tr.props().item.availableByFacility[0].facilityCode === 'DC6' )
    const allIcons = lockedRow.findAllComponents( Icon )

    const notAvailableCell = lockedRow.find( '[data-availability-mobile="not-available"]' )

    const desktopLockedIcon = allIcons.find( i => i.attributes()['data-icon-desktop'] === 'locked' )
    const mobileLockedIcon = allIcons.find( i => i.attributes()['data-icon-mobile'] === 'locked' )

    expect( notAvailableCell.exists()).toBe ( false )
    expect( desktopLockedIcon ).toBeUndefined()
    expect( mobileLockedIcon ).toBeUndefined()
  })

  it( 'renders inventory item rows in the table when items are provided', async () => {

    inventoryItems.value = [
      {
        id:                  814659,
        sku:                 'test-product-001',
        title:               'Test Product 001',
        availableQuantity:   0,
        availableByFacility: null,
        backOrdered:         0,
        price:               0,
        supplier:            '',
        kitComponents:       null,
        locked:              true
      },
      {
        id:                  631469,
        sku:                 'RideOnDVD',
        title:               'Ride On DVD',
        availableQuantity:   5,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 5,
            lots:           null
          }
        ],
        backOrdered:   0,
        price:         24.95,
        supplier:      '',
        kitComponents: null,
        locked:        false
      }
    ]

    await newWrapper.vm.$nextTick()

    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    expect( tableRows.length ).toBeGreaterThan( 0 )

  })

  it( 'disables adding products that are not available in the chosen facility on desktop and mobile', async () => {

    // Find all table rows
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    const lockedRow = tableRows.find( tr => tr.props().item.availableByFacility === null )
    const allIcons = lockedRow.findAllComponents( Icon )

    const notAvailableCell = lockedRow.find( '[data-availability-mobile="not-available"]' )

    const desktopLockedIcon = allIcons.find( i => i.attributes()['data-icon-desktop'] === 'locked' )
    const mobileLockedIcon = allIcons.find( i => i.attributes()['data-icon-mobile'] === 'locked' )

    expect( notAvailableCell.text()).toBe( 'Not Available' )
    expect( desktopLockedIcon.exists()).toBe( true )
    expect( mobileLockedIcon.exists()).toBe( true )

  })

  it( 'displays the add button on available items on desktop and mobile', async () => {

    // Find all table rows
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    // Find the row that should be locked
    const availableRow = tableRows.find( tr => tr.props().item.availableByFacility !== null )

    // Check both lock icons
    const allIcons = availableRow.findAllComponents( Icon )
    const desktopLockedIcon = allIcons.find( i => i.attributes()['data-icon-desktop'] === 'locked' )
    const mobileLockedIcon = allIcons.find( i => i.attributes()['data-icon-mobile'] === 'locked' )

    expect( desktopLockedIcon ).toBe( undefined )
    expect( mobileLockedIcon ).toBe( undefined )

    const addButtons = availableRow.findAllComponents( Button )

    const addButtonDesktop = addButtons.find( b => b.attributes()['data-button-desktop'] === 'addItem' )
    expect( addButtonDesktop.exists()).toBe( true )

    const addButtonMobile = addButtons.find( b => b.attributes()['data-button-button'] === 'addItem' )
    expect( addButtonMobile.exists()).toBe( true )

  })

  it( 'displays the inventory items cells correctly for mobile and desktop and buttons work as expected', async () => {

    // Find all table rows
    const tableRows = newWrapper.findAllComponents( CreateOrderInventoryProduct )

    // Find the row that should NOT be locked
    const availableRow = tableRows.find( tr => tr.props().item.availableByFacility !== null )

    const addButtons = availableRow.findAllComponents( Button )
    const addButtonDesktop = addButtons.find( b => b.attributes()['data-button-desktop'] === 'addItem' )
    const addButtonMobile = addButtons.find( b => b.attributes()['data-button-button'] === 'addItem' )

    // Check the sku cell
    expect( availableRow.find( '[data-sku-mobile="sku"]' ).exists()).toBe( true )
    expect( availableRow.find( '[data-sku-mobile="sku"]' ).text()).toBe( 'RideOnDVD' )

    // Check the title cell
    expect( availableRow.find( '[data-title-mobile="title"]' ).exists()).toBe( true )
    expect( availableRow.find( '[data-title-mobile="title"]' ).text()).toBe( 'Ride On DVD' )

    // Check the availability cell
    expect( availableRow.find( '[data-price-mobile="price"]' ).exists()).toBe( true )
    expect( availableRow.find( '[data-price-mobile="price"]' ).text()).toBe( '$24.95' )

    // Check the availability cell
    expect( availableRow.find( '[data-availability-mobile-text="qty"]' ).exists()).toBe( true )
    expect( availableRow.find( '[data-availability-mobile-text="qty"]' ).text()).toBe( 'QTY: 5' )

    await addButtonDesktop.trigger( 'click' )
    await newWrapper.vm.$nextTick()

    // Hide the add button after clicking it
    expect( addButtonDesktop.exists()).toBe( false )
    expect( addButtonMobile.exists()).toBe( false )

    const newButtons = availableRow.findAllComponents( Button )

    // Show Add/Remove QTY buttons for mobile
    const removeQtyButton = newButtons.find( b => b.attributes()['data-button-button'] === 'removeQty' )
    const addQtyButton = newButtons.find( b => b.attributes()['data-button-button'] === 'addQty' )

    expect( removeQtyButton.exists()).toBe( true )
    expect( addQtyButton.exists()).toBe( true )

    // Get the QTY input from the row
    const allInputs = availableRow.findAllComponents( InputField ) as any[]

    // Only the mobile input is in this component. The desktop input is in another table
    const mobileQtyInput = allInputs.find( i => i.attributes()['data-input-mobile'] === 'inputQty' )

    expect( mobileQtyInput.exists()).toBe( true )
    expect( mobileQtyInput.props().modelValue ).toBe( 1 )

    // Increase QTY
    await addQtyButton.trigger( 'click' )

    // Check if QTY is increased
    expect( mobileQtyInput.props().modelValue ).toBe( 2 )

    // Decrease QTY
    await removeQtyButton.trigger( 'click' )
    expect( mobileQtyInput.props().modelValue ).toBe( 1 )

    // Click the decrease QTY again(remove the item)
    await removeQtyButton.trigger( 'click' )

    // add/remove qty buttons and input should be gone
    expect( removeQtyButton.exists()).toBe( false )
    expect( addQtyButton.exists()).toBe( false )
    expect( mobileQtyInput.exists()).toBe( false )

    const afterRemoveButtons = availableRow.findAllComponents( Button )
    const newAddButtonDesktop = afterRemoveButtons.find( b => b.attributes()['data-button-desktop'] === 'addItem' )
    const newAddButtonMobile = afterRemoveButtons.find( b => b.attributes()['data-button-button'] === 'addItem' )

    expect( newAddButtonDesktop.exists()).toBe( true )
    expect( newAddButtonMobile.exists()).toBe( true )

  })

  it( 'opens the selected items mobile sidebar and shows the selected products', async () => {

    const openSelectedButton = newWrapper.find( '[data-button-mobile="open-selected"]' )

    await openSelectedButton.trigger( 'click' )

    const selectedItemsSidebar = newWrapper.findComponent( Sidebar )

    expect( selectedItemsSidebar.props().open ).toBe( true )

  })

  it( 'shows the correct selected item cells when product is NOT in stock on mobile', async () => {
    selectedItems.value = [
      {
        id:                  426182,
        cost:                0,
        available:           0,
        requested:           1,
        description:         'Salt Bike a Year With the Baron (download)',
        backordered:         0,
        partReference:       'SaltBike-Baron-dl',
        declaredValue:       1,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 99998,
            lots:           null
          }
        ]
      }
    ]

    // Find the button that opens the selected items sidebar
    const openSelectedButton = newWrapper.find( '[data-button-mobile="open-selected"]' )

    // Trigger click to open the selected items sidebar
    await openSelectedButton.trigger( 'click' )

    // Find the selected items sidebar
    const selectedItemsSidebar = newWrapper.findComponent( Sidebar )

    // Make sure it's the correct sidebar
    expect( selectedItemsSidebar.props().name ).toBe( 'selected-items' )

    // Make sure it's opened
    expect( selectedItemsSidebar.props().open ).toBe( true )

    // Find the Products table in the sidebar
    const productsTable = selectedItemsSidebar.findComponent( Products )

    // Check if it exists
    expect( productsTable.exists()).toBe( true )

    const allRows = productsTable.findAllComponents( Row )

    // For one selected items, we have two rows because the header row is a row as well.
    expect( allRows.length ).toBe( 2 )

    const selectedRow =  allRows.find( r => r.attributes()['data-row'] === 'selected-product' )

    expect( selectedRow.exists()).toBe( true )

    // Mobile SKU cell exists and has the correct text
    expect( selectedRow.find( '[data-reference-mobile="selected-part-reference"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-reference-mobile="selected-part-reference"]' ).text()).toBe( 'SaltBike-Baron-dl' )

    // Mobile DESCRIPTION cell exists and has the correct text
    expect( selectedRow.find( '[data-description-mobile="selected-description"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-description-mobile="selected-description"]' ).text()).toBe( 'Salt Bike a Year With the Baron (download)' )

    // Mobile removeQty button exists
    expect( selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).exists()).toBe( true )

    const allInputs = selectedRow.findAllComponents( InputField )

    // Mobile qty INPUT exists and has the correct value
    const mobileInputQty = allInputs.find( i => i.attributes()['data-input-mobile'] === 'selected-qty' )

    expect( mobileInputQty.exists()).toBe( true )
    expect( mobileInputQty.props().modelValue ).toBe( 1 )

    // Mobile addQty button exists
    expect( selectedRow.find( '[data-button-mobile="selected-addQty"]' ).exists()).toBe( true )

    // If no backorder
    expect( selectedRow.find( '[data-backorder-mobile="no-backorder"]' ).exists()).toBe( false )

    // If backorder
    expect( selectedRow.find( '[data-backorder-mobile="backorder"]' ).text()).toBe( '0 / 1 BO' )

    // Price input has the correct value
    const priceInput = allInputs.find( i => i.attributes()['data-input-mobile'] === 'price' )
    expect( priceInput.exists()).toBe( true )
    expect( priceInput.props().modelValue ).toBe( 1 )

    // Total should have the correct text
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Click the mobile increase button
    await selectedRow.find( '[data-button-mobile="selected-addQty"]' ).trigger( 'click' )

    // Assert that the input value has been increased by 1
    expect( mobileInputQty.props().modelValue ).toBe( 2 )

    // Make sure the backorder value is correct
    expect( selectedRow.find( '[data-backorder-mobile="backorder"]' ).text()).toBe( '0 / 2 BO' )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $2.00' )

    // Click the mobile decrease button
    await selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).trigger( 'click' )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Make sure backorder qty has been reduced after quantity decrease
    expect( selectedRow.find( '[data-backorder-mobile="backorder"]' ).text()).toBe( '0 / 1 BO' )

    // Click the mobile decrease button again to remove the item
    await selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).trigger( 'click' )

    // No rows should be shown
    expect( selectedRow.exists()).toBe( false )

  })

  it( 'shows close button for the selected items sidebar', async () => {

    selectedItems.value = [
      {
        id:                  426182,
        cost:                0,
        available:           99998,
        requested:           1,
        description:         'Salt Bike a Year With the Baron (download)',
        backordered:         0,
        partReference:       'SaltBike-Baron-dl',
        declaredValue:       1,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 99998,
            lots:           null
          }
        ]
      }
    ]

    // Get the button that opens the selected items sidebar
    const openSelectedButton = newWrapper.find( '[data-button-mobile="open-selected"]' )

    // Trigger click to open it
    await openSelectedButton.trigger( 'click' )

    // Find the selected items sidebar
    const selectedItemsSidebar = newWrapper.findComponent( Sidebar )
    const selectedItemsButton = selectedItemsSidebar.findComponent( Button )

    // Ensure that it's the correct sidebar
    expect( selectedItemsSidebar.props().name ).toBe( 'selected-items' )

    // Ensure that it's open after clicking the button
    expect( selectedItemsSidebar.props().open ).toBe( true )

    // Assert that the close button exists
    expect( selectedItemsButton.attributes()['data-button-mobile'] ).toBe( 'close-selected' )

    // Assert that the close button holds the correct items count
    expect( selectedItemsButton.attributes()['data-button-selected-count'] ).toBe( '1' )

    // Click the close button to close the selected items sidebar
    await selectedItemsButton.trigger( 'click' )

    // Assert that the close button closes the selected items sidebar
    expect( selectedItemsSidebar.props().open ).toBe( false )
  })

  it( 'shows the correct selected item cells when product is in stock on mobile', async () => {

    // Item is added in the previous test

    // Find the button that opens the selected items sidebar
    const openSelectedButton = newWrapper.find( '[data-button-mobile="open-selected"]' )

    // Trigger click to open the selected items sidebar
    await openSelectedButton.trigger( 'click' )

    // Find the selected items sidebar
    const selectedItemsSidebar = newWrapper.findComponent( Sidebar )

    // Make sure it's the correct sidebar
    expect( selectedItemsSidebar.props().name ).toBe( 'selected-items' )

    // Make sure it's opened
    expect( selectedItemsSidebar.props().open ).toBe( true )

    // Find the Products table in the sidebar
    const productsTable = selectedItemsSidebar.findComponent( Products )

    // Check if it exists
    expect( productsTable.exists()).toBe( true )

    const allRows = productsTable.findAllComponents( Row )

    // For one selected items, we have two rows because the header row is a row as well.
    expect( allRows.length ).toBe( 2 )

    const selectedRow =  allRows.find( r => r.attributes()['data-row'] === 'selected-product' )

    expect( selectedRow.exists()).toBe( true )

    // Mobile SKU cell exists and has the correct text
    expect( selectedRow.find( '[data-reference-mobile="selected-part-reference"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-reference-mobile="selected-part-reference"]' ).text()).toBe( 'SaltBike-Baron-dl' )

    // Mobile DESCRIPTION cell exists and has the correct text
    expect( selectedRow.find( '[data-description-mobile="selected-description"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-description-mobile="selected-description"]' ).text()).toBe( 'Salt Bike a Year With the Baron (download)' )

    // Mobile removeQty button exists
    expect( selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).exists()).toBe( true )

    const allInputs = selectedRow.findAllComponents( InputField )

    // Mobile qty INPUT exists and has the correct value
    const mobileInputQty = allInputs.find( i => i.attributes()['data-input-mobile'] === 'selected-qty' )

    expect( mobileInputQty.exists()).toBe( true )
    expect( mobileInputQty.props().modelValue ).toBe( 1 )

    // Mobile addQty button exists
    expect( selectedRow.find( '[data-button-mobile="selected-addQty"]' ).exists()).toBe( true )

    // If no backorder
    expect( selectedRow.find( '[data-backorder-mobile="no-backorder"]' ).text()).toBe( '99997' )

    // Price input has the correct value
    const priceInput = allInputs.find( i => i.attributes()['data-input-mobile'] === 'price' )
    expect( priceInput.exists()).toBe( true )
    expect( priceInput.props().modelValue ).toBe( 1 )

    // Total should have the correct text
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Click the mobile increase button
    await selectedRow.find( '[data-button-mobile="selected-addQty"]' ).trigger( 'click' )

    // Assert that the input value has been increased by 1
    expect( mobileInputQty.props().modelValue ).toBe( 2 )

    // Make sure available has been reduced after quantity increase
    expect( selectedRow.find( '[data-backorder-mobile="no-backorder"]' ).text()).toBe( '99996' )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $2.00' )

    // Click the mobile decrease button
    await selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).trigger( 'click' )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Make sure available has been reduced after quantity decrease
    expect( selectedRow.find( '[data-backorder-mobile="no-backorder"]' ).text()).toBe( '99997' )

    // Click the mobile decrease button again to remove the item
    await selectedRow.find( '[data-button-mobile="selected-removeQty"]' ).trigger( 'click' )

    // No rows should be shown
    expect( selectedRow.exists()).toBe( false )

  })

  it( 'shows the correct selected item cells when product is in NOT stock on desktop', async () => {

    // Item must be added again, the last thing in the previous test is removing an item so selectedItems will have no items.
    selectedItems.value = [
      {
        id:                  426182,
        cost:                0,
        available:           0,
        requested:           1,
        description:         'Salt Bike a Year With the Baron (download)',
        backordered:         0,
        partReference:       'SaltBike-Baron-dl',
        declaredValue:       1,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 99998,
            lots:           null
          }
        ]
      }
    ]

    await newWrapper.vm.$nextTick()

    const productsTable = newWrapper.findComponent( Products )

    expect( productsTable.exists()).toBe( true )

    const allRows = productsTable.findAllComponents( Row )

    // Get the selected row
    const selectedRow = allRows.find( r => r.attributes()['data-row'] === 'selected-product' )

    // Get all inputs
    const allInputs = newWrapper.findAllComponents( InputField ) as any[]

    expect( selectedRow.exists()).toBe( true )

    // Sku cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-sku-desktop="selected-sku"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-sku-desktop="selected-sku"]' ).text()).toBe( 'SaltBike-Baron-dl' )

    // Description cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-description-desktop="selected-description"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-description-desktop="selected-description"]' ).text()).toBe( 'Salt Bike a Year With the Baron (download)' )

    // Backordered cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-available-desktop="selected-no-backordered"]' ).exists()).toBe( false )
    expect( selectedRow.find( '[data-available-desktop="selected-backordered"]' ).text()).toBe( '0 /' )
    expect( selectedRow.find( '[data-backordered-desktop="backordered-BO"]' ).text()).toBe( '0 / 1 BO' )

    // Backorder Availability cell for desktop should be hidden
    expect( selectedRow.find( 'data-available-desktop="selected-backordered"' ).exists()).toBe( false )

    // Price input has the correct value
    const priceInput = allInputs.find( i => i.attributes()['data-input-desktop'] === 'price' )
    expect( priceInput.exists()).toBe( true )
    expect( priceInput.props().modelValue ).toBe( 1 )

    // Remove QTY button exists
    expect( selectedRow.find( '[data-button-desktop="removeQty"]' ).exists()).toBe( true )

    // Qty input exists and has the correct value
    const qtyInput = allInputs.find( i => i.attributes()['data-input-desktop'] === 'qty' )
    expect( qtyInput.exists()).toBe( true )
    expect( qtyInput.props().modelValue ).toBe( 1 )

    // Add QTY button exists
    expect( selectedRow.find( '[data-button-desktop="addQty"]' ).exists()).toBe( true )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Click the addQuantity
    await selectedRow.find( '[data-button-desktop="addQty"]' ).trigger( 'click' )

    // Backordered quantity has increased
    expect( selectedRow.find( '[data-available-desktop="selected-no-backordered"]' ).exists()).toBe( false )
    expect( selectedRow.find( '[data-backordered-desktop="backordered-BO"]' ).text()).toBe( '0 / 2 BO' )

    // Assert that quantity is updated
    expect( qtyInput.props().modelValue ).toBe( 2 )

    // Assert total is updated
    expect( selectedRow.find( '[data-total-desktop="total-price"]' ).text()).toBe( '$2.00' )

  })
  it( 'shows the correct selected item cells when product is in stock on desktop', async () => {

    // Item must be added again, the last thing in the previous test is removing an item so selectedItems will have no items.
    selectedItems.value = [
      {
        id:                  426182,
        cost:                0,
        available:           99998,
        requested:           1,
        description:         'Salt Bike a Year With the Baron (download)',
        backordered:         0,
        partReference:       'SaltBike-Baron-dl',
        declaredValue:       1,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 99998,
            lots:           null
          }
        ]
      }
    ]

    await newWrapper.vm.$nextTick()

    const productsTable = newWrapper.findComponent( Products )

    expect( productsTable.exists()).toBe( true )

    const allRows = productsTable.findAllComponents( Row )

    // Get the selected row
    const selectedRow = allRows.find( r => r.attributes()['data-row'] === 'selected-product' )

    // Get all inputs
    const allInputs = newWrapper.findAllComponents( InputField ) as any[]

    expect( selectedRow.exists()).toBe( true )

    // Sku cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-sku-desktop="selected-sku"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-sku-desktop="selected-sku"]' ).text()).toBe( 'SaltBike-Baron-dl' )

    // Description cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-description-desktop="selected-description"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-description-desktop="selected-description"]' ).text()).toBe( 'Salt Bike a Year With the Baron (download)' )

    // Availability cell for desktop exists and has the correct value
    expect( selectedRow.find( '[data-available-desktop="selected-no-backordered"]' ).exists()).toBe( true )
    expect( selectedRow.find( '[data-available-desktop="selected-no-backordered"]' ).text()).toBe( '99997' )

    // Backorder Availability cell for desktop should be hidden
    expect( selectedRow.find( 'data-available-desktop="selected-backordered"' ).exists()).toBe( false )

    // Price input has the correct value
    const priceInput = allInputs.find( i => i.attributes()['data-input-desktop'] === 'price' )
    expect( priceInput.exists()).toBe( true )
    expect( priceInput.props().modelValue ).toBe( 1 )

    // Remove QTY button exists
    expect( selectedRow.find( '[data-button-desktop="removeQty"]' ).exists()).toBe( true )

    // Qty input exists and has the correct value
    const qtyInput = allInputs.find( i => i.attributes()['data-input-desktop'] === 'qty' )
    expect( qtyInput.exists()).toBe( true )
    expect( qtyInput.props().modelValue ).toBe( 1 )

    // Add QTY button exists
    expect( selectedRow.find( '[data-button-desktop="addQty"]' ).exists()).toBe( true )

    // Total should have the correct text after update
    expect( selectedRow.find( '[data-total-mobile="total-price"]' ).text()).toBe( 'Total: $1.00' )

    // Click the addQuantity
    await selectedRow.find( '[data-button-desktop="addQty"]' ).trigger( 'click' )

    expect( selectedRow.find( '[data-available-desktop="selected-no-backordered"]' ).text()).toBe( '99996' )

    // Assert that quantity is updated
    expect( qtyInput.props().modelValue ).toBe( 2 )

    // Assert total is updated
    expect( selectedRow.find( '[data-total-desktop="total-price"]' ).text()).toBe( '$2.00' )

  })

  it( 'enables the Confirm Products(next step) button after items are added', async () => {

    const allButtons = newWrapper.findAllComponents( Button )
    const confirmItemsButton = allButtons.find( b => b.attributes()['data-button-button'] )

    expect( confirmItemsButton.exists()).toBe( true )
    expect( confirmItemsButton.props().disabled ).toBe( false )
  })

})

describe( 'create order - [view]', () => {

  let wrapper = null

  beforeEach(() => {

    wrapper = shallowMount( CreateOrder, {
      stubs: {
        Loader,
      },
    })

  })

  it( 'renders the view', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'shows loader when initialPending is true', async () => {

    wrapper.vm.initialPending = true

    await wrapper.vm.$nextTick()

    const loader = wrapper.findComponent( Loader )

    expect( loader.exists()).toBe( true )

  })

  it( 'hides loader when initialPending is false', async () => {

    wrapper.vm.initialPending = false

    await wrapper.vm.$nextTick()

    const loader = wrapper.findComponent( Loader )

    expect( loader.exists()).toBe( false )

  })

  it( 'renders DESKTOP Client and Order details when initialPending is false and the step is 1', async () => {

    wrapper.vm.initialPending = false

    activeStep.value = 1
    await wrapper.vm.$nextTick()

    const orderDetails = wrapper.find( '[data-component-desktop="order"]' )
    const clientDetails = wrapper.find( '[data-component-desktop="client"]' )
    const products = wrapper.find( '[data-component-desktop="products"]' )
    const summary = wrapper.find( '[data-component-desktop="summary"]' )
    const shipping = wrapper.find( '[data-component-desktop="shipping"]' )
    const footer = wrapper.find( '[data-component-desktop="footer"]' )

    // Show the order details and client details. Hide products, summary and shipping
    expect( orderDetails.exists()).toBe( true )
    expect( clientDetails.exists()).toBe( true )

    expect( products.exists()).toBe( false )
    expect( summary.exists()).toBe( false )
    expect( shipping.exists()).toBe( false )
    expect( footer.exists()).toBe( false )

  })

  it( 'renders DESKTOP Client and Order details when initialPending is false and the step is 2', async () => {

    activeStep.value = 2

    await wrapper.vm.$nextTick()

    const orderDetails = wrapper.find( '[data-component-desktop="order"]' )
    const clientDetails = wrapper.find( '[data-component-desktop="client"]' )
    const products = wrapper.find( '[data-component-desktop="products"]' )
    const summary = wrapper.find( '[data-component-desktop="summary"]' )
    const shipping = wrapper.find( '[data-component-desktop="shipping"]' )
    const footer = wrapper.find( '[data-component-desktop="footer"]' )

    // Show the order details and client details. Hide products, summary and shipping
    expect( orderDetails.exists()).toBe( true )
    expect( clientDetails.exists()).toBe( true )

    expect( products.exists()).toBe( false )
    expect( summary.exists()).toBe( false )
    expect( shipping.exists()).toBe( false )
    expect( footer.exists()).toBe( false )

  })

  it ( 'renders DESKTOP CreateOrderInventory and Products components when step is 3', async () => {

    activeStep.value = 3

    await wrapper.vm.$nextTick()

    const orderDetails = wrapper.find( '[data-component-desktop="order"]' )
    const clientDetails = wrapper.find( '[data-component-desktop="client"]' )
    const inventory = wrapper.find( '[data-component-desktop="inventory"]' )
    const products = wrapper.find( '[data-component-desktop="products"]' )
    const summary = wrapper.find( '[data-component-desktop="summary"]' )
    const shipping = wrapper.find( '[data-component-desktop="shipping"]' )
    const footer = wrapper.find( '[data-component-desktop="footer"]' )

    expect( orderDetails.exists()).toBe( false )
    expect( clientDetails.exists()).toBe( false )

    expect( inventory.exists()).toBe( true )
    expect( products.exists()).toBe( true )

    expect( summary.exists()).toBe( false )
    expect( shipping.exists()).toBe( false )
    expect( footer.exists()).toBe( false )

  })

  it ( 'renders DESKTOP Summary and Shipping components when step is 4', async () => {

    activeStep.value = 4

    await wrapper.vm.$nextTick()

    const orderDetails = wrapper.find( '[data-component-desktop="order"]' )
    const clientDetails = wrapper.find( '[data-component-desktop="client"]' )
    const inventory = wrapper.find( '[data-component-desktop="inventory"]' )
    const products = wrapper.find( '[data-component-desktop="products"]' )
    const summary = wrapper.find( '[data-component-desktop="summary"]' )
    const shipping = wrapper.find( '[data-component-desktop="shipping"]' )
    const footer = wrapper.find( '[data-component-desktop="footer"]' )

    expect( orderDetails.exists()).toBe( false )
    expect( clientDetails.exists()).toBe( false )

    expect( inventory.exists()).toBe( false )
    expect( products.exists()).toBe( false )

    expect( summary.exists()).toBe( true )
    expect( shipping.exists()).toBe( true )
    expect( footer.exists()).toBe( true )

  })

  it( 'renders Create Draft Order title and create-order-icon if isDraftOrder is true', async () => {

    // Set the value of `isDraftOrder` to true
    isDraftOrder.value = true
    await wrapper.vm.$nextTick()

    // Check if the draft order icon and text are rendered by default
    expect( wrapper.find( '[data-title-icon="create-order-icon"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-title="create-order-title"]' ).text()).toBe( 'Create Draft Order' )

  })
  it( 'renders Create Live Order title and hides create-order-icon if isDraftOrder is false', async () => {

    // Set the value of `isDraftOrder` to false
    isDraftOrder.value = false
    await wrapper.vm.$nextTick()

    // Check that the draft order icon is not rendered
    expect( wrapper.find( '[data-title-icon="create-order-icon"]' ).exists()).toBe( false )

    // Check if the text changes to 'Create Live Order'
    expect( wrapper.find( '[data-title="create-order-title"]' ).text()).toBe( 'Create Live Order' )

  })

  it( 'enables the Confirm Customer Details button when both clientStepValid and orderStepValid are true and after clicking the step is changed', async () => {

    // Mock the computed properties
    vi.spyOn( wrapper.vm, 'clientStepValid', 'get' ).mockReturnValueOnce( true )
    vi.spyOn( wrapper.vm, 'orderStepValid', 'get' ).mockReturnValueOnce( true )

    // Set initial conditions
    wrapper.vm.initialPending = false
    activeStep.value = 1

    // Force a re-render after state changes
    await wrapper.vm.$nextTick()

    const allButtons = wrapper.findAllComponents( Button )
    const confirmCustomerDetails = allButtons.find( b =>
      b.attributes()['data-button-desktop'] === 'confirm-customer-details'
    )

    expect( confirmCustomerDetails.exists()).toBe( true )
    expect( confirmCustomerDetails.props().disabled ).toBe( false )

    await confirmCustomerDetails.trigger( 'click' )

    expect( activeStep.value ).toBe( 3 )
    expect( wrapper.vm.activeFlow ).toBe( 3 )

  })

  it( 'disables the Confirm Customer Details button when clientStepValid is false and orderStepValid is true', async () => {

    wrapper.vm.initialPending = false

    // Mock clientStepValid and orderStepValid
    vi.spyOn( wrapper.vm, 'clientStepValid', 'get' ).mockReturnValueOnce( false )
    vi.spyOn( wrapper.vm, 'orderStepValid', 'get' ).mockReturnValueOnce( true )

    activeStep.value = 1

    await wrapper.vm.$nextTick()

    // Find the button component
    const allButtons = wrapper.findAllComponents( Button )
    const confirmCustomerDetails = allButtons.find( b => b.attributes()['data-button-desktop'] === 'confirm-customer-details' )

    // Check if the button exists
    expect( confirmCustomerDetails.exists()).toBe( true )

    // Check if the button is disabled
    expect( confirmCustomerDetails.props().disabled ).toBe( true )

  })

  it( 'disables the Confirm Customer Details button when clientStepValid is true and orderStepValid is false', async () => {

    wrapper.vm.initialPending = false

    // Mock clientStepValid and orderStepValid
    vi.spyOn( wrapper.vm, 'clientStepValid', 'get' ).mockReturnValue( true )
    vi.spyOn( wrapper.vm, 'orderStepValid', 'get' ).mockReturnValue( false )

    activeStep.value = 1

    await wrapper.vm.$nextTick()

    // Find the button component
    const allButtons = wrapper.findAllComponents( Button )
    const confirmCustomerDetails = allButtons.find( b => b.attributes()['data-button-desktop'] === 'confirm-customer-details' )

    // Check if the button exists
    expect( confirmCustomerDetails.exists()).toBe( true )

    // Check if the button is disabled
    expect( confirmCustomerDetails.props().disabled ).toBe( true )

  })

  it( 'disables the Confirm Products button when productsStepValid is false', async () => {

    wrapper.vm.initialPending = false

    // Mock clientStepValid and orderStepValid
    vi.spyOn( wrapper.vm, 'productsStepValid', 'get' ).mockReturnValueOnce( false )

    activeStep.value = 3

    await wrapper.vm.$nextTick()

    // Find the button component
    const allButtons = wrapper.findAllComponents( Button )
    const confirmProducts = allButtons.find( b => b.attributes()['data-button-desktop'] === 'confirm-products' )

    // Check if the button exists
    expect( confirmProducts.exists()).toBe( true )

    // Check if the button is disabled
    expect( confirmProducts.props().disabled ).toBe( true )

  })

  it( 'enables the Confirm Products button when productsStepValid is true', async () => {

    wrapper.vm.initialPending = false

    // Mock productsStepValid
    vi.spyOn( wrapper.vm, 'productsStepValid', 'get' ).mockReturnValueOnce( true )

    activeStep.value = 3

    await wrapper.vm.$nextTick()

    // Find the button component
    const allButtons = wrapper.findAllComponents( Button )
    const confirmProducts = allButtons.find( b => b.attributes()['data-button-desktop'] === 'confirm-products' )

    // Check if the button exists
    expect( confirmProducts.exists()).toBe( true )

    // Check if the button is disabled
    expect( confirmProducts.props().disabled ).toBe( false )

  })

  it( 'shows the correct total value based on the items previously added', async () => {

    activeStep.value = 4

    await wrapper.vm.$nextTick()

    const total = wrapper.find( '[data-total-desktop="total"]' )
    expect( total.text()).toBe( 'Total: $2.00 USD' )

  })

  it( 'renders MOBILE Client component when activeStep is 1', async () => {

    activeStep.value = 1

    await wrapper.vm.$nextTick()

    const mobileClientComponent = wrapper.find( '[data-component-mobile="client"]' )
    const mobileOrderComponent = wrapper.find( '[data-component-mobile="order"]' )
    const mobileInventoryComponent = wrapper.find( '[data-component-mobile="inventory"]' )
    const mobileShippingComponent = wrapper.find( '[data-component-mobile="shipping"]' )
    const mobileSummaryComponent = wrapper.find( '[data-component-mobile="summary"]' )

    expect( mobileClientComponent.exists()).toBe( true )

    expect( mobileOrderComponent.exists()).toBe( false )
    expect( mobileInventoryComponent.exists()).toBe( false )
    expect( mobileShippingComponent.exists()).toBe( false )
    expect( mobileSummaryComponent.exists()).toBe( false )

  })

  it( 'renders MOBILE Order component when activeStep is 2', async () => {

    activeStep.value = 2

    await wrapper.vm.$nextTick()

    const mobileClientComponent = wrapper.find( '[data-component-mobile="client"]' )
    const mobileOrderComponent = wrapper.find( '[data-component-mobile="order"]' )
    const mobileInventoryComponent = wrapper.find( '[data-component-mobile="inventory"]' )
    const mobileShippingComponent = wrapper.find( '[data-component-mobile="shipping"]' )
    const mobileSummaryComponent = wrapper.find( '[data-component-mobile="summary"]' )

    expect( mobileOrderComponent.exists()).toBe( true )

    expect( mobileClientComponent.exists()).toBe( false )
    expect( mobileInventoryComponent.exists()).toBe( false )
    expect( mobileShippingComponent.exists()).toBe( false )
    expect( mobileSummaryComponent.exists()).toBe( false )

  })

  it( 'renders MOBILE Inventory component when activeStep is 3', async () => {

    activeStep.value = 3

    await wrapper.vm.$nextTick()

    const mobileClientComponent = wrapper.find( '[data-component-mobile="client"]' )
    const mobileOrderComponent = wrapper.find( '[data-component-mobile="order"]' )
    const mobileInventoryComponent = wrapper.find( '[data-component-mobile="inventory"]' )
    const mobileShippingComponent = wrapper.find( '[data-component-mobile="shipping"]' )
    const mobileSummaryComponent = wrapper.find( '[data-component-mobile="summary"]' )

    expect( mobileInventoryComponent.exists()).toBe( true )

    expect( mobileClientComponent.exists()).toBe( false )
    expect( mobileOrderComponent.exists()).toBe( false )
    expect( mobileShippingComponent.exists()).toBe( false )
    expect( mobileSummaryComponent.exists()).toBe( false )

  })

  it( 'renders MOBILE Shipping component when activeStep is 4', async () => {

    activeStep.value = 4

    await wrapper.vm.$nextTick()

    const mobileClientComponent = wrapper.find( '[data-component-mobile="client"]' )
    const mobileOrderComponent = wrapper.find( '[data-component-mobile="order"]' )
    const mobileInventoryComponent = wrapper.find( '[data-component-mobile="inventory"]' )
    const mobileShippingComponent = wrapper.find( '[data-component-mobile="shipping"]' )
    const mobileSummaryComponent = wrapper.find( '[data-component-mobile="summary"]' )

    expect( mobileShippingComponent.exists()).toBe( true )

    expect( mobileClientComponent.exists()).toBe( false )
    expect( mobileOrderComponent.exists()).toBe( false )
    expect( mobileInventoryComponent.exists()).toBe( false )
    expect( mobileSummaryComponent.exists()).toBe( false )

  })

  it( 'renders MOBILE Summary component when activeStep is 5', async () => {

    activeStep.value = 5

    await wrapper.vm.$nextTick()

    const mobileClientComponent = wrapper.find( '[data-component-mobile="client"]' )
    const mobileOrderComponent = wrapper.find( '[data-component-mobile="order"]' )
    const mobileInventoryComponent = wrapper.find( '[data-component-mobile="inventory"]' )
    const mobileShippingComponent = wrapper.find( '[data-component-mobile="shipping"]' )
    const mobileSummaryComponent = wrapper.find( '[data-component-mobile="summary"]' )

    expect( mobileSummaryComponent.exists()).toBe( true )

    expect( mobileClientComponent.exists()).toBe( false )
    expect( mobileOrderComponent.exists()).toBe( false )
    expect( mobileInventoryComponent.exists()).toBe( false )
    expect( mobileShippingComponent.exists()).toBe( false )

  })

})

describe( 'create order - [component:Client]', () => {

  let wrapper = null

  beforeEach(() => {

    wrapper = mount( Client )

  })

  it( 'renders the client component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'renders the correct fields as mandatory', async () => {

    const inputs = wrapper.findAllComponents( InputField )
    const selects = wrapper.findAllComponents( '[data-input-type="select"]' )
    const mandatoryFieldLabels = [ 'First Name', 'Last Name', 'Shipping Address Line 1', 'State / Province', 'City', 'Postal Code' ]

    mandatoryFieldLabels.forEach(( l ) => {

      const input = inputs.find( i => i.props().label === l )

      expect( input.props().required ).toBe( true )

    })

    // Check for the selects as well
    selects.forEach(( s ) => {

      if ( s.props().label )
        expect( s.props().nullable ).toBe( false )

    })

  })

  it( 'sets USA as shipping country to default country on mount', async () => {

    const allSelects = wrapper.findAllComponents( '[data-input-type="select"]' )
    const countrySelect = allSelects.find( s => s.props().label === 'Country' )

    expect( shipping.value.country.value ).toBe( 'USA' )
    expect( countrySelect.props().modelValue ).toBe( 'USA' )
    expect( countrySelect.exists()).toBe( true )

  })

  it( 'disables mobile next step button if no data is entered in the form', async () => {

    const allButtons = wrapper.findAllComponents( Button )

    const mobileNextStep = allButtons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )

    expect( mobileNextStep.text()).toBe( 'Confirm Client Details' )
    expect( mobileNextStep.props().disabled ).toBe( true )

  })

  it( 'does not copy shipping info to billing when isSameInfo is not checked and next step mobile button is disabled', async () => {

    shipping.value.firstName.value = 'John'
    shipping.value.lastName.value = 'Doe'
    shipping.value.companyName.value = 'Company Inc'
    shipping.value.email.value = '<EMAIL>'
    shipping.value.address1.value = '123 Main St'
    shipping.value.address2.value = 'Apt 4B'
    shipping.value.country.value = 'USA'
    shipping.value.state.value = 'CA'
    shipping.value.city.value = 'Los Angeles'
    shipping.value.zip.value = '90001'
    shipping.value.phone.value = '************'

    isSameInfo.value = false

    await wrapper.vm.$nextTick()

    expect( billing.value.firstName.value ).toBe( null )
    expect( billing.value.lastName.value ).toBe( null )
    expect( billing.value.companyName.value ).toBe( null )
    expect( billing.value.email.value ).toBe( null )
    expect( billing.value.address1.value ).toBe( null )
    expect( billing.value.address2.value ).toBe( null )

    // Country has a default value
    expect( billing.value.country.value ).toBe( 'USA' )

    expect( billing.value.state.value ).toBe( null )
    expect( billing.value.city.value ).toBe( null )
    expect( billing.value.zip.value ).toBe( null )
    expect( billing.value.phone.value ).toBe( null )

    expect( clientStepValid.value ).toBe( false )

    const allButtons = wrapper.findAllComponents( Button )

    const mobileNextStep = allButtons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )

    expect( mobileNextStep.text()).toBe( 'Confirm Client Details' )
    expect( mobileNextStep.props().disabled ).toBe( true )

  })

  it( 'copies all shipping info to billing when isSameInfo is checked and next step mobile button is NOT disabled', async () => {

    shipping.value.firstName.value = 'John'
    shipping.value.lastName.value = 'Doe'
    shipping.value.companyName.value = 'Company Inc'
    shipping.value.email.value = '<EMAIL>'
    shipping.value.address1.value = '123 Main St'
    shipping.value.address2.value = 'Apt 4B'
    shipping.value.country.value = 'USA'
    shipping.value.state.value = 'CA'
    shipping.value.city.value = 'Los Angeles'
    shipping.value.zip.value = '90001'
    shipping.value.phone.value = '************'

    isSameInfo.value = true

    await wrapper.vm.$nextTick()

    expect( billing.value.firstName.value ).toBe( 'John' )
    expect( billing.value.lastName.value ).toBe( 'Doe' )
    expect( billing.value.companyName.value ).toBe( 'Company Inc' )
    expect( billing.value.email.value ).toBe( '<EMAIL>' )
    expect( billing.value.address1.value ).toBe( '123 Main St' )
    expect( billing.value.address2.value ).toBe( 'Apt 4B' )
    expect( billing.value.country.value ).toBe( 'USA' )
    expect( billing.value.state.value ).toBe( 'CA' )
    expect( billing.value.city.value ).toBe( 'Los Angeles' )
    expect( billing.value.zip.value ).toBe( '90001' )
    expect( billing.value.phone.value ).toBe( '************' )

    expect( clientStepValid.value ).toBe( true )

    const allButtons = wrapper.findAllComponents( Button )

    const mobileNextStep = allButtons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )

    expect( mobileNextStep.text()).toBe( 'Confirm Client Details' )
    expect( mobileNextStep.props().disabled ).toBe( false )

  })

  it( 'updates activeStep when the Confirm Client Details (next step) button is clicked', async () => {

    activeStep.value = 1

    expect( activeStep.value ).toBe( 1 )

    const buttons = wrapper.findAllComponents( Button )
    const confirmButton = buttons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )

    await confirmButton.trigger( 'click' )

    expect( confirmButton.text()).toBe( 'Confirm Client Details' )
    expect( activeStep.value ).toBe( 2 )

  })

})

describe( 'create order - [component:Order]', () => {

  const wrapper = mount( Order )

  it( 'renders the Order component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'selects DRAFT order option on the Order State radio by default', () => {

    const allRadioButtons = wrapper.findAllComponents( Radio )
    const orderStateRadio = allRadioButtons.find( b => b.props().label === 'Order State' )

    expect( isDraftOrder.value ).toBe( true )
    expect( orderStateRadio.props().modelValue ).toBe( 1 )
    expect( orderStateRadio.props().options ).toStrictEqual( [
      {
        id:   1,
        name: 'Draft Order'
      },
      {
        id:   0,
        name: 'Live Order'
      }
    ]
    )

    expect( orderStateRadio.props().required ).toBe( true )

  })

  it( 'should have the correct default values and required fields for a DRAFT order', () => {

    const clientOrderReference = wrapper.findComponent( InputField ) as any
    const allRadioButtons = wrapper.findAllComponents( Radio )

    const orderTypeRadio = allRadioButtons.find( b => b.props().label === 'Order Type' )
    const orderTax = allRadioButtons.find( b => b.props().label === 'International Tax And Duty' )
    const shipmentFacility = allRadioButtons.find( b => b.props().label === 'Shipment Facility' )

    // Shipment Facility
    expect( shipmentFacility.props().required ).toBe( true )
    expect( shipmentFacility.props().modelValue ).toBe( 'DC1' )
    expect( shipmentFacility.props().options ).toStrictEqual( [ {
      id:   'DC1',
      name: 'Midwest - Mobridge, SD',
    } ] )
    expect( shipmentFacility.attributes().style ).toBe( 'display: none;' )

    // International Tax And Duty tests
    expect( orderTax.props().required ).toBe( true )
    expect( orderTax.props().modelValue ).toBe( 0 )
    expect( orderTax.props().options ).toStrictEqual( [
      {
        id:   1,
        name: 'DDP Delivered Duty Paid'
      },
      {
        id:   0,
        name: 'DDU Delivered Duty Unpaid'
      }
    ]
    )

    // Order Type tests
    expect( orderTypeRadio.props().required ).toBe( true )
    expect( orderTypeRadio.props().modelValue ).toBe( 0 )
    expect( orderTypeRadio.props().options ).toStrictEqual( [
      {
        id:   0,
        name: 'Consumer'
      },
      {
        id:   1,
        name: 'Business'
      }
    ] )

    // Client Order Reference tests
    expect( clientOrderReference.props().label ).toBe( 'Client Order Reference' )
    expect( clientOrderReference.props().required ).toBe( true )

    // Confirm Customer Details button is disabled
    const allButtons = wrapper.findAllComponents( Button )
    const nextStepButton = allButtons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )
    expect( nextStepButton.props().disabled ).toBe( true )

  })

  it( 'is able to continue to the next step for DRAFT order if all mandatory fields are filled, assuming client data is valid', async () => {

    isDraftOrder.value = true

    const allButtons = wrapper.findAllComponents( Button )
    const nextStepButton = allButtons.find( b => b.attributes()['data-button-mobile'] === 'next-step' )

    expect( clientStepValid.value ).toBe( true )
    expect( nextStepButton.props().disabled ).toBe( true )

    await wrapper.vm.$nextTick()

    // Now, update other fields
    details.value.paymentType.value = 'CC'
    details.value.clientReference.value = '123ref'
    details.value.isBusinessOrder.value = false
    details.value.isDdp.value = false
    details.value.isGift.value = false

    await wrapper.vm.$nextTick()

    expect( orderStepValid.value ).toBe( true )
    expect( nextStepButton.props().disabled ).toBe( false )

  })

  it( 'renders the Order State radio correctly and updates isDraftOrder and paymentTypeTemp when LIVE order is selected', async () => {

    const allRadioButtons = wrapper.findAllComponents( Radio )
    const orderStateRadio = allRadioButtons.find( r => r.props().label === 'Order State' )

    expect( orderStateRadio.props().options ).toEqual( [
      { id: 1, name: 'Draft Order' },
      { id: 0, name: 'Live Order' },
    ] )

    // Trigger the update event to select 'Live Order'
    await orderStateRadio.vm.$emit( 'update:modelValue', 0 )

    // Check if isDraftOrder is updated correctly
    expect( isDraftOrder.value ).toBe( false )

    // Trigger the update event to select 'Draft Order'
    await orderStateRadio.vm.$emit( 'update:modelValue', 1 )

    // Check if isDraftOrder is updated correctly
    expect( isDraftOrder.value ).toBe( true )

  })

  it( 'disables Payment Type, fills with correct value, and makes Client Order Reference non-mandatory when Live Order is selected, disables the Payment Type select', async () => {

    const allRadioButtons = wrapper.findAllComponents( Radio )
    const orderStateRadio = allRadioButtons.find( r => r.props().label === 'Order State' )

    // Select 'Live Order'
    await orderStateRadio.vm.$emit( 'update:modelValue', 0 )

    // Check if Client Order Reference is no longer mandatory
    const allInputs = wrapper.findAllComponents( InputField ) as any[]
    const clientOrderReferenceInput = allInputs.find( i => i.props().label === 'Client Order Reference' )
    expect( clientOrderReferenceInput.props().required ).toBe( false )

    // The step should be valid with null value because client reference is not mandatory for live orders
    expect( orderStepValid.value ).toBe( true )

  })

  it( 'hides the Gift Message input when "Use Gift Invoice" is not selected', async () => {

    details.value.isGift.value = false

    await wrapper.vm.$nextTick()

    const textBoxFields = wrapper.findAllComponents( '[data-input-type="textarea"]' ) as any[]
    const giftMessageField = textBoxFields.find( t => t?.props()?.label === 'Optional Gift Message (up to size of box)' )

    expect( giftMessageField ).toBe( undefined )

  })

  it( 'renders the Gift Message input when "Use Gift Invoice" is selected', async () => {

    details.value.isGift.value = true

    await wrapper.vm.$nextTick()

    const textBoxFields = wrapper.findAllComponents( '[data-input-type="textarea"]' ) as any[]
    const giftMessageField = textBoxFields.find( t => t?.props()?.label === 'Optional Gift Message (up to size of box)' )

    expect( giftMessageField.exists()).toBe( true )

  })

  it( 'renders the shipment facility radio for a client with multiple facilities', async () => {

    details.value.facilityRule.value = null
    details.value.facilityRule.valid = false
    defaultFacility.value = 'ALL'

    facilitiesList.value = [
      {
        id:   'DC1',
        name: 'Midwest - Mobridge, SD'
      },
      {
        id:   'DC6',
        name: 'West - Los Angeles, CA'
      },
      {
        id:   'DC7',
        name: 'East - Wilmington, OH'
      }
    ]

    await wrapper.vm.$nextTick()

    const allRadioButtons = wrapper.findAllComponents( Radio )
    const shipmentFacilityRadio = allRadioButtons.find( r => r.props().label === 'Shipment Facility' )

    expect( shipmentFacilityRadio.props().required ).toBe( true )
    expect( shipmentFacilityRadio.props().modelValue ).toBe( null )
    expect( shipmentFacilityRadio.props().valid ).toBe( false )
    expect( shipmentFacilityRadio.props().options ).toStrictEqual(
      [
        {
          id:   'DC1',
          name: 'Midwest - Mobridge, SD',
        },
        {
          id:   'DC6',
          name: 'West - Los Angeles, CA',
        },
        {
          id:   'DC7',
          name: 'East - Wilmington, OH',
        }
      ]
    )

    details.value.facilityRule.value = 'DC6'
    await wrapper.vm.$nextTick()

    expect( shipmentFacilityRadio.props().modelValue ).toBe( 'DC6' )
    expect( shipmentFacilityRadio.props().valid ).toBe( true )

  })

})

describe( 'create order - [component:Summary]', () => {

  const wrapper = mount( Summary )

  it( 'renders the summary component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'renders shipping details correctly', () => {

    // Shipping info check
    expect( wrapper.find( '[data-shipping="shipping"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping="name"]' ).text()).toBe( 'John Doe' )
    expect( wrapper.find( '[data-shipping="address"]' ).text()).toBe( '123 Main St Apt 4B' )
    expect( wrapper.find( '[data-shipping="city"]' ).text()).toBe( 'Los Angeles, 90001' )
    expect( wrapper.find( '[data-shipping="country"]' ).text()).toBe( 'USA' )

  })

  it( 'renders billing details correctly', () => {

    // billing info check
    expect( wrapper.find( '[data-billing="billing"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-billing="name"]' ).text()).toBe( 'John Doe' )
    expect( wrapper.find( '[data-billing="address"]' ).text()).toBe( '123 Main St Apt 4B' )
    expect( wrapper.find( '[data-billing="city"]' ).text()).toBe( 'Los Angeles, 90001' )
    expect( wrapper.find( '[data-billing="country"]' ).text()).toBe( 'USA' )

  })

  it( 'renders the order line items table on the summary step', () => {

    const lineItemsTable = wrapper.findComponent( Table )
    const rows = lineItemsTable.findAllComponents( Row )

    expect( selectedItems.value.length ).toBe( 1 )

    // The first row is the header row
    expect( rows.length ).toBeGreaterThan( 1 )

    expect( lineItemsTable.exists()).toBe( true )

  })

  it( 'renders the correct total', () => {

    expect( wrapper.find( '[data-total="summary-total"]' ).exists()).toBe( true )

    // Items are added in the state already
    expect( wrapper.find( '[data-total="summary-total"]' ).text()).toBe( 'Total: $2.00 USD' )

  })

  it( 'goes to step 1', async () => {

    expect( wrapper.find( '[data-button="go-to-client"]' ).exists()).toBe( true )

    await wrapper.find( '[data-button="go-to-client"]' ).trigger( 'click' )

    expect( activeStep.value ).toBe( 1 )

    expect( wrapper.find( '[data-button="go-to-client-details"]' ).exists()).toBe( true )

    await wrapper.find( '[data-button="go-to-client-details"]' ).trigger( 'click' )

    expect( activeStep.value ).toBe( 1 )

  })

  it( 'goes to step 3', async () => {

    const table  = wrapper.findComponent( Table )
    const allTableButtons = table.findAllComponents( Button )

    const backToStep3Button  = allTableButtons.find( b => b.props().icon === 'edit' )

    expect( backToStep3Button.exists()).toBe( true )

    await backToStep3Button.trigger( 'click' )

    expect( activeStep.value ).toBe( 3 )

  })

  it( 'goes to step 4', async () => {

    expect( wrapper.find( '[data-button="go-to-shipping"]' ).exists()).toBe( true )

    await wrapper.find( '[data-button="go-to-shipping"]' ).trigger( 'click' )

    expect( activeStep.value ).toBe( 4 )

  })

})

describe( 'create order - [component:Shipping]', () => {

  const wrapper = mount( Shipping )

  it( 'loads the shipping step', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'disables the confirm carrier (next-step) button if no shipping method is chosen', () => {

    expect( shippingStepValid.value ).toBe( false )

    const nextStepButton = wrapper.findComponent( Button )

    expect( nextStepButton.text()).toBe( 'Confirm Carrier' )
    expect( nextStepButton.props().disabled ).toBe( true )

  })

  it( 'lists the correct carriers', () => {

    expect( wrapper.find( '[data-shipping-method="UPS"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping-method="FedEx"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping-method="DHL"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping-method="USPS"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping-method="Flat Rate"]' ).exists()).toBe( true )
    expect( wrapper.find( '[data-shipping-method="Other"]' ).exists()).toBe( true )

    expect( wrapper.find( '[data-shipping-method="UPS"]' ).text()).toBe( 'UPS' )
    expect( wrapper.find( '[data-shipping-method="FedEx"]' ).text()).toBe( 'FedEx' )
    expect( wrapper.find( '[data-shipping-method="DHL"]' ).text()).toBe( 'DHL' )
    expect( wrapper.find( '[data-shipping-method="USPS"]' ).text()).toBe( 'USPS' )
    expect( wrapper.find( '[data-shipping-method="Flat Rate"]' ).text()).toBe( 'Flat Rate' )
    expect( wrapper.find( '[data-shipping-method="Other"]' ).text()).toBe( 'Other' )

  })

  it( 'lists the correct options for the carriers and changes the options based on the carrier', async () => {

    // set UPS options
    shippingGroups[0].options = [
      {
        id:   'UPS.2DA',
        name: 'UPS 2nd Day Air'
      },
      {
        id:   'UPS.2AM',
        name: 'UPS 2nd Day Air A.M.'
      },
      {
        id:   'UPS.3DA',
        name: 'UPS 3 Day Select'
      },
      {
        id:   'UPS.WEPD',
        name: 'UPS Expedited'
      },
      {
        id:   'UPS.WEXP',
        name: 'UPS Express'
      },
      {
        id:   'TANDATA_UPS.UPS.WEXPPLS',
        name: 'UPS Express Plus'
      },
      {
        id:   'TANDATA_UPS.UPS.WEXPSVR',
        name: 'UPS Express Saver'
      },
      {
        id:   'UPS.GND',
        name: 'UPS Ground'
      },
      {
        id:   'UPS.NDA',
        name: 'UPS Next Day Air'
      },
      {
        id:   'UPS.NAM',
        name: 'UPS Next Day Air A.M.'
      },
      {
        id:   'UPS.NDS',
        name: 'UPS Next Day Air Saver'
      },
      {
        id:   'UPS.STDCAMX',
        name: 'UPS Standard Canada'
      },
      {
        id:   'UPS.SPPS',
        name: 'UPS SurePost 1 lb or Greater'
      },
      {
        id:   'UPS.SPSTD',
        name: 'UPS SurePost Less than 1 lb'
      }
    ]

    // Set FedEx options
    shippingGroups[1].options = [
      {
        id:   'FDX.2DA',
        name: 'FedEx 2Day'
      },
      {
        id:   'FDX.ECO',
        name: 'FedEx Express Saver'
      },
      {
        id:   'FDX.GND',
        name: 'FedEx Ground'
      },
      {
        id:   'FDX.HD',
        name: 'FedEx Home Delivery'
      },
      {
        id:   'TANDATA_FEDEXFSMS.FEDEX.IECO',
        name: 'FedEx International Economy'
      },
      {
        id:   'TANDATA_FEDEXFSMS.FEDEX.IPRI',
        name: 'FedEx International Priority'
      },
      {
        id:   'FDX.PRI',
        name: 'FedEx Priority Overnight'
      },
      {
        id:   'TANDATA_FEDEXFSMS.FEDEX.SP_PS',
        name: 'FedEx SmartPost Parcel Select'
      },
      {
        id:   'TANDATA_FEDEXFSMS.FEDEX.SP_STD',
        name: 'FedEx SmartPost Standard Mail'
      },
      {
        id:   'FDX.STD',
        name: 'FedEx Standard Overnight'
      }
    ]

    await wrapper.vm.$nextTick()

    const fedexButton = wrapper.find( '[data-shipping-method="UPS"]' )
    expect( fedexButton.exists()).toBe( true )

    const shipRadio = wrapper.findComponent( Radio )

    expect( shipRadio.exists()).toBe( true )
    expect( shipRadio.props().options ).toBe( shippingGroups[0].options )

    await fedexButton.trigger( 'click' )

    expect( shipRadio.props().options ).toBe( shippingGroups[1].options )

    shipping.value.shipType.value = 'FDX.2DA'
    expect( shipping.value.shipType.valid ).toBe( true )

  })

  it( 'enables the button after shipping method is chosen', () => {

    expect( shippingStepValid.value ).toBe( true )

    const nextStepButton = wrapper.findComponent( Button )

    expect( nextStepButton.props().disabled ).toBe( false )

  })

})

describe( 'create order - [component:Footer]', () => {

  const wrapper = mount( CreateOrderFooter )

  vi.spyOn( wrapper.vm, 'shippingStepValid', 'get' ).mockReturnValue( true )

  it( 'renders create order footer ', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'renders Release Order and Hold Order buttons and hides other buttons when the order has only available items', async () => {

    details.value.facilityRule.value = 'DC6'

    selectedItems.value = [
      {
        id:                  814552,
        cost:                0,
        available:           1187,
        requested:           1,
        description:         'KEYCHAIN DOG SILVER',
        backordered:         0,
        partReference:       'ACCESS_SIL_DOG',
        declaredValue:       0,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 0,
            lots:           null
          },
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 1187,
            lots:           null
          },
          {
            facilityName:   'East - Wilmington, OH',
            facilityCode:   'DC7',
            unitsAvailable: 0,
            lots:           null
          }
        ],
        kitComponents: null
      }
    ]

    const wrapper = mount( CreateOrderFooter )

    expect( wrapper.vm.canShipPartialOrder ).toBe( false )
    expect( wrapper.vm.canShipEntireOrder ).toBe( true )

    const allButtons = wrapper.findAllComponents( Button )

    const backorderButton = allButtons.find( b => b.text() === 'Backorder' )
    const holdOrderButton = allButtons.find( b => b.text() === 'Hold Order' )
    const shipInStockButton = allButtons.find( b => b.text() === 'Ship In Stock Now' )
    const releaseOrderButton = allButtons.find( b => b.text() === 'Release Order for Shipping' )
    const backorderEntireButton = allButtons.find( b => b.text() === 'Backorder Entire Order' )

    expect( holdOrderButton.exists()).toBe( true )
    expect( releaseOrderButton.exists()).toBe( true )

    expect( backorderEntireButton ).toBeUndefined()
    expect( backorderButton ).toBeUndefined()
    expect( shipInStockButton ).toBeUndefined()

  })

  it( 'renders Hold Order and Ship In Stock Now buttons and hides other buttons when there are items with and without backorder', async () => {

    details.value.facilityRule.value = 'DC6'

    selectedItems.value = [
      {
        id:                  814552,
        cost:                0,
        available:           1187,
        requested:           1,
        description:         'KEYCHAIN DOG SILVER',
        backordered:         0,
        partReference:       'ACCESS_SIL_DOG',
        declaredValue:       0,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 0,
            lots:           null
          },
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 1187,
            lots:           null
          },
          {
            facilityName:   'East - Wilmington, OH',
            facilityCode:   'DC7',
            unitsAvailable: 0,
            lots:           null
          }
        ],
        kitComponents: null
      },
      {
        id:                  814660,
        cost:                0,
        available:           0,
        requested:           1,
        description:         'Test-Integration',
        backordered:         1,
        partReference:       'Test-Integration',
        declaredValue:       0,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 0,
            lots:           null
          },
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 0,
            lots:           null
          }
        ],
        kitComponents: null
      }
    ]

    const wrapper = mount( CreateOrderFooter )

    const allButtons = wrapper.findAllComponents( Button )

    expect( wrapper.vm.canShipPartialOrder ).toBe( true )
    expect( wrapper.vm.canShipEntireOrder ).toBe( false )

    const backorderButton = allButtons.find( b => b.text() === 'Backorder' )
    const holdOrderButton = allButtons.find( b => b.text() === 'Hold Order' )
    const shipInStockButton = allButtons.find( b => b.text() === 'Ship In Stock Now' )
    const releaseOrderButton = allButtons.find( b => b.text() === 'Release Order for Shipping' )
    const backorderEntireButton = allButtons.find( b => b.text() === 'Backorder Entire Order' )

    expect( holdOrderButton.exists()).toBe( true )
    expect( backorderEntireButton.exists()).toBe( true )
    expect( shipInStockButton.exists()).toBe( true )

    expect( releaseOrderButton ).toBeUndefined()
    expect( backorderButton ).toBeUndefined()

  })

  it( 'renders Hold Order and Backorder when none of the items are available', async () => {

    details.value.facilityRule.value = 'DC6'

    selectedItems.value = [
      {
        id:                  814660,
        cost:                0,
        available:           0,
        requested:           1,
        description:         'Test-Integration',
        backordered:         1,
        partReference:       'Test-Integration',
        declaredValue:       0,
        availableByFacility: [
          {
            facilityName:   'Midwest - Mobridge, SD',
            facilityCode:   'DC1',
            unitsAvailable: 0,
            lots:           null
          },
          {
            facilityName:   'West - Los Angeles, CA',
            facilityCode:   'DC6',
            unitsAvailable: 0,
            lots:           null
          }
        ],
        kitComponents: null
      }
    ]

    await wrapper.vm.$nextTick()

    expect( wrapper.vm.canShipPartialOrder ).toBe( false )
    expect( wrapper.vm.canShipEntireOrder ).toBe( false )

    const allButtons = wrapper.findAllComponents( Button )

    const backorderButton = allButtons.find( b => b.text() === 'Backorder' )
    const holdOrderButton = allButtons.find( b => b.text() === 'Hold Order' )
    const shipInStockButton = allButtons.find( b => b.text() === 'Ship In Stock Now' )
    const releaseOrderButton = allButtons.find( b => b.text() === 'Release Order for Shipping' )
    const backorderEntireButton = allButtons.find( b => b.text() === 'Backorder Entire Order' )

    expect( holdOrderButton.exists()).toBe( true )
    expect( backorderButton.exists()).toBe( true )

    expect( releaseOrderButton ).toBeUndefined()
    expect( backorderEntireButton ).toBeUndefined()
    expect( shipInStockButton ).toBeUndefined()

  })

  describe( 'creates live order', async () => {

    const createReleaseOrderSpy = vi.spyOn( wrapper.vm, 'createReleasedOrder' )
    const createBackorderedOrderSpy = vi.spyOn( wrapper.vm, 'createBackorderedOrder' )
    const createOnHoldOrderSpy = vi.spyOn( wrapper.vm, 'createOnHoldOrder' )
    const createPartialOrderSpy = vi.spyOn( wrapper.vm, 'createPartialOrder' )

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it( 'creates on hold order', async () => {

      isDraftOrder.value = false
      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const holdOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'hold-order' )

      await wrapper.vm.$nextTick()

      expect( holdOrderButton.exists()).toBe( true )
      expect( holdOrderButton.props().disabled ).toBe( false )

      await holdOrderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'HOLDORDER' )
      expect( createOnHoldOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).not.toHaveBeenCalled()

    })

    it( 'creates partial order', async () => {

      isDraftOrder.value = false
      selectedItems.value = [
        {
          id:                  814552,
          cost:                0,
          available:           1187,
          requested:           1,
          description:         'KEYCHAIN DOG SILVER',
          backordered:         0,
          partReference:       'ACCESS_SIL_DOG',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 1187,
              lots:           null
            },
            {
              facilityName:   'East - Wilmington, OH',
              facilityCode:   'DC7',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        },
        {
          id:                  814660,
          cost:                0,
          available:           0,
          requested:           1,
          description:         'Test-Integration',
          backordered:         1,
          partReference:       'Test-Integration',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        }
      ]

      details.value.facilityRule.value = 'DC6'

      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const holdOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'create-partial' )

      expect( holdOrderButton.exists()).toBe( true )
      expect( holdOrderButton.props().disabled ).toBe( false )

      await holdOrderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'PARTIALSHIP' )

      expect( createPartialOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).not.toHaveBeenCalled()

    })

    it( 'creates backordered order', async () => {

      isDraftOrder.value = false
      selectedItems.value = [
        {
          id:                  814552,
          cost:                0,
          available:           1187,
          requested:           1,
          description:         'KEYCHAIN DOG SILVER',
          backordered:         0,
          partReference:       'ACCESS_SIL_DOG',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 1187,
              lots:           null
            },
            {
              facilityName:   'East - Wilmington, OH',
              facilityCode:   'DC7',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        },
        {
          id:                  814660,
          cost:                0,
          available:           0,
          requested:           1,
          description:         'Test-Integration',
          backordered:         1,
          partReference:       'Test-Integration',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        }
      ]

      details.value.facilityRule.value = 'DC6'

      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const backorderButton = allButtons.find( b => b.attributes()['data-button'] === 'backorder-entire' )

      expect( backorderButton.exists()).toBe( true )
      expect( backorderButton.props().disabled ).toBe( false )

      await backorderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'BACKORDER' )

      expect( createBackorderedOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).not.toHaveBeenCalled()

    })

    it( 'creates release order', async () => {

      isDraftOrder.value = false
      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const releaseOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'release-order' )

      expect( releaseOrderButton.exists()).toBe( true )
      expect( releaseOrderButton.props().disabled ).toBe( false )

      await releaseOrderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'NOBACKORDER' )

      expect( createReleaseOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).not.toHaveBeenCalled()

    })

  })

  describe( 'create draft order', async () => {

    const createReleaseOrderSpy = vi.spyOn( wrapper.vm, 'createReleasedOrder' )
    const createBackorderedOrderSpy = vi.spyOn( wrapper.vm, 'createBackorderedOrder' )
    const createOnHoldOrderSpy = vi.spyOn( wrapper.vm, 'createOnHoldOrder' )
    const createPartialOrderSpy = vi.spyOn( wrapper.vm, 'createPartialOrder' )

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it( 'creates on hold order', async () => {

      const allButtons = wrapper.findAllComponents( Button )
      const holdOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'hold-order' )

      await wrapper.vm.$nextTick()

      expect( holdOrderButton.exists()).toBe( true )
      expect( holdOrderButton.props().disabled ).toBe( false )

      await holdOrderButton.trigger( 'click' )

      await wrapper.vm.$nextTick()

      expect( wrapper.vm.backorderRule ).toBe( 'HOLDORDER' )
      expect( createOnHoldOrderSpy ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).not.toHaveBeenCalled()

    })

    it( 'creates partial order', async () => {

      selectedItems.value = [
        {
          id:                  814552,
          cost:                0,
          available:           1187,
          requested:           1,
          description:         'KEYCHAIN DOG SILVER',
          backordered:         0,
          partReference:       'ACCESS_SIL_DOG',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 1187,
              lots:           null
            },
            {
              facilityName:   'East - Wilmington, OH',
              facilityCode:   'DC7',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        },
        {
          id:                  814660,
          cost:                0,
          available:           0,
          requested:           1,
          description:         'Test-Integration',
          backordered:         1,
          partReference:       'Test-Integration',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        }
      ]

      details.value.facilityRule.value = 'DC6'

      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const holdOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'create-partial' )

      expect( holdOrderButton.exists()).toBe( true )
      expect( holdOrderButton.props().disabled ).toBe( false )

      await holdOrderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'PARTIALSHIP' )
      expect( createPartialOrderSpy ).toHaveBeenCalledOnce()
      expect( createDraftOrder ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).not.toHaveBeenCalled()

    })

    it( 'creates backordered order', async () => {

      selectedItems.value = [
        {
          id:                  814552,
          cost:                0,
          available:           1187,
          requested:           1,
          description:         'KEYCHAIN DOG SILVER',
          backordered:         0,
          partReference:       'ACCESS_SIL_DOG',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 1187,
              lots:           null
            },
            {
              facilityName:   'East - Wilmington, OH',
              facilityCode:   'DC7',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        },
        {
          id:                  814660,
          cost:                0,
          available:           0,
          requested:           1,
          description:         'Test-Integration',
          backordered:         1,
          partReference:       'Test-Integration',
          declaredValue:       0,
          availableByFacility: [
            {
              facilityName:   'Midwest - Mobridge, SD',
              facilityCode:   'DC1',
              unitsAvailable: 0,
              lots:           null
            },
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          kitComponents: null
        }
      ]

      details.value.facilityRule.value = 'DC6'

      await wrapper.vm.$nextTick()

      const allButtons = wrapper.findAllComponents( Button )
      const backorderButton = allButtons.find( b => b.attributes()['data-button'] === 'backorder-entire' )

      expect( backorderButton.exists()).toBe( true )
      expect( backorderButton.props().disabled ).toBe( false )

      await backorderButton.trigger( 'click' )
      await wrapper.vm.$nextTick()
      expect( wrapper.vm.backorderRule ).toBe( 'BACKORDER' )

      expect( createDraftOrder ).toHaveBeenCalledOnce()
      expect( createBackorderedOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).not.toHaveBeenCalled()

    })

    it( 'creates release order', async () => {

      const allButtons = wrapper.findAllComponents( Button )
      const releaseOrderButton = allButtons.find( b => b.attributes()['data-button'] === 'release-order' )

      expect( releaseOrderButton.exists()).toBe( true )
      expect( releaseOrderButton.props().disabled ).toBe( false )

      await releaseOrderButton.trigger( 'click' )

      expect( wrapper.vm.backorderRule ).toBe( 'NOBACKORDER' )

      expect( createDraftOrder ).toHaveBeenCalledOnce()
      expect( createReleaseOrderSpy ).toHaveBeenCalledOnce()
      expect( createLiveOrder ).not.toHaveBeenCalled()

    })

  })

})
