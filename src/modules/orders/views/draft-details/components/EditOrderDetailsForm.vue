<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { setNotificationOptions } from '@lib/store/snackbar'
import { defaultFacility, facilitiesList } from '@/store'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { backorderRulesList, orderTypes, updateDraftOrder } from '@/modules/orders/store'
import { convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Radio from '@lib/components/inputs/Radio.vue'
import Button from '@lib/components/button/Button.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import OptionsGroup from '@lib/components/blocks/OptionsGroup.vue'

import type { ComponentPublicInstance } from 'vue'
import type { DraftOrder, OrderBilling } from '@/modules/orders/types'

const props = defineProps<{
  order?: DraftOrder
}>()

const emits = defineEmits<{
  ( event: 'close' ): void
  ( event: 'update' ): void
}>()

const isBusinessOrder   = ref<{ value: 1 | 0, valid: boolean }>({ value: props.order.isBusinessOrder === true ? 1 : 0, valid: true })

const orderDetails      = props.order
const billing           = { ...props.order.billing, poNumber: orderDetails.poNumber || null }

const orderDetailsOmittedKeys: Array<keyof DraftOrder> = Object.keys( orderDetails ) as Array<keyof DraftOrder>

const billingModel      = reactive<Validatable<OrderBilling>>( convertObjectToValidatable( billing, null, [ 'companyName', 'address2', 'email', 'phone', 'paymentType' ] ))
const orderDetailsModel = reactive<Validatable<DraftOrder>>( convertObjectToValidatable( orderDetails, null, orderDetailsOmittedKeys ))

const canUpdate         = computed(() => validateModel( orderDetailsModel ))
const hasFacility       = ref<boolean>()
const updatePending     = defineModel<boolean>( 'pending' )
const giftMessageRef    = ref<ComponentPublicInstance>( null )

async function updateOrderDetails() {

  updatePending.value = true

  const orderDetails = convertObjectToPlain( orderDetailsModel )
  const billing      = convertObjectToPlain( billingModel )

  const orderData: DraftOrder = {
    ...orderDetails,
    importStatus: props.order.importStatus === 'Failed' ? 'Draft' : props.order.importStatus,
    billing
  }

  const { error } = await updateDraftOrder( props.order.id, orderData )

  if ( !error ) {
    emits( 'update' )
    setNotificationOptions({ message: 'Order details are updated successfully.' })
  }

  updatePending.value = false

}

onMounted(() => {

  // If the client has a default facility, use that as the facility rule value.
  // This should happened once when the create order view is mounted.

  orderDetailsModel.facilityCode.value = defaultFacility.value === 'ALL'
    ? orderDetailsModel.facilityCode.value
    : defaultFacility.value

  // If there is a default facility, set the hasFacility flag to true.

  hasFacility.value = defaultFacility.value !== 'ALL'

})

defineExpose({ orderDetailsModel, billingModel, updateOrderDetails, canUpdate })

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">

    <form
      class="w-full md:w-[46rem] md:max-w-[46rem] md:px-2 grid gap-4 overflow-hidden overflow-y-auto content-start"
      :class="{ 'pointer-events-none': updatePending }"
      @submit.prevent
    >

      <!-- Order Details Section -->

      <section class="md:px-4 pb-4 grid md:grid-cols-2 md:gap-4">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Order Details
          </p>
        </div>

        <InputField
          v-model="orderDetailsModel.clientReference.value"
          v-model:valid="orderDetailsModel.clientReference.valid"
          label="Client Reference Number"
          :required="true"
        />

        <InputField
          v-model="billingModel.poNumber.value"
          v-model:valid="billingModel.poNumber.valid"
          label="PO Number"
          :required="false"
        />

        <InputField
          v-model="orderDetailsModel.groupName.value"
          v-model:valid="orderDetailsModel.groupName.valid"
          label="Group Name"
          :required="false"
        />

        <InputField
          v-model="orderDetailsModel.backorderRule.value"
          v-model:valid="orderDetailsModel.backorderRule.valid"
          type="select"
          label="Backorder Rule"
          :options="backorderRulesList"
          :nullable="false"
        />

        <div class="grid w-full md:col-span-2">
          <Radio
            v-show="!hasFacility"
            v-model="orderDetailsModel.facilityCode.value"
            v-model:valid="orderDetailsModel.facilityCode.valid"
            :options="facilitiesList"
            :required="true"
            label="Shipment Facility"
            :disabled="hasFacility"
          />

          <Radio
            v-model="isBusinessOrder.value"
            v-model:valid="isBusinessOrder.valid"
            :options="orderTypes"
            :required="true"
            label="Order Type"
            @update:model-value="(value) => orderDetailsModel.isBusinessOrder.value = !!value"
          />

          <div class="md:col-span-2 flex flex-col gap-4">

            <OptionsGroup label="Gift Options">

              <Checkbox

                v-model="orderDetailsModel.isGift.value"
                @update:model-value="async (value) => {
                  if (value){
                    await nextTick()
                    giftMessageRef?.$el?.scrollIntoView({ behavior: 'smooth' })
                  }
                }"
              >
                Use Gift Invoice
              </Checkbox>

            </OptionsGroup>

            <InputField
              v-if="orderDetailsModel.isGift.value"
              ref="giftMessageRef"
              v-model="orderDetailsModel.giftMessage.value"
              v-model:valid="orderDetailsModel.giftMessage.valid"
              type="textarea"
              label="Optional Gift Message (up to size of box)"
              :required="false"
            />

          </div>
        </div>
      </section>

    </form>

    <div class="w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">

      <Button
        data-button="cancel"
        size="auto"
        mode="secondary"
        class="px-4"
        :disabled="updatePending"
        @click="emits('close')"
      >
        {{ $t('global.button.cancel') }}
      </Button>

      <Button
        data-button="update"
        size="auto"
        class="px-4"
        :pending="updatePending"
        :disabled="!canUpdate"
        @click="updateOrderDetails"
      >
        Save Info
      </Button>

    </div>

  </div>

</template>
