<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { addEvent } from '@/modules/orders/store'
import { userName } from '@/modules/auth/store'
import { formatDate } from '@lib/scripts/utils'
import { computed, ref } from 'vue'

import Tab from '@lib/components/buttons/tab/Tab.vue'
import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

import type { OrderEvent } from '@/modules/orders/types'

const props = defineProps<{
  events:  OrderEvent[]
  orderId: number
}>()

const emits = defineEmits<{
  ( event: 'update:events', payload: OrderEvent[] ): void
}>()

const note        = ref<string>( null )
const pending     = ref<boolean>( false )
const eventsMode  = ref<'events' | 'notes'>( 'events' )

const orderEvents = computed({
  get: () => props.events,
  set: ( value: OrderEvent[] ) => emits( 'update:events', value )

})

async function saveNote() {

  pending.value = true

  const { error, payload } = await addEvent( props.orderId, {
    type:    1,
    message: note.value,
    creator: userName.value
  })

  if ( !error ) {
    note.value = null
    orderEvents.value = payload?.events ?? []
  }

  pending.value = false

}

</script>

<template>

  <div class="w-full md:w-[21rem] h-full grid grid-rows-[max-content_1fr_max-content] bg-core-10 border-r border-l border-core-30 overflow-hidden">

    <div class="w-full h-[2.55rem] grid grid-cols-2">

      <Tab
        class="border-r border-r-core-40"
        :active="eventsMode === 'events'"
        label="Events"
        @click="eventsMode = 'events'"
      />

      <Tab
        :label="$t('orders.details.comments')"
        :active="eventsMode === 'notes'"
        @click="eventsMode = 'notes'"
      />

    </div>

    <div class="w-full overflow-hidden overflow-y-auto">

      <Transition name="source-form" mode="out-in">

        <div v-if="eventsMode === 'events'">

          <div
            v-for="event in events.filter(e => e.type !== 1)"
            :key="event.id"
            class="w-full p-4 grid grid-rows-[max-content_max-content] items-start border-b border-core-30"
          >

            <p class="text-sm">
              {{ event.message }}
            </p>

            <div>
              <p class="text-xs text-core-60">
                {{ event.creator }} - {{ formatDate(event.createdAt, 'MMM DD, YYYY [at] HH:mm') }}
              </p>
            </div>

          </div>

        </div>

        <div v-else>

          <div
            v-for="event in events.filter(e => e.type === 1)"
            :key="event.id"
            class="w-full p-4 grid grid-rows-[max-content_max-content] items-start border-b border-core-30"
          >

            <p class="text-sm ">
              {{ event.message }}
            </p>

            <div>
              <p class="text-xs text-core-60">
                {{ event.creator }} - {{ formatDate(event.createdAt, 'MMM DD, YYYY [at] HH:mm') }}
              </p>
            </div>

          </div>

        </div>

      </Transition>

    </div>

    <Guard scope="Order.Write">

      <form
        v-if="eventsMode === 'notes'"
        class="p-4 grid grid-cols-[1fr_max-content] gap-x-2 items-center bg-core-20 border-t border-core-30"
        @submit.prevent="saveNote"
      >

        <InputField
          v-model="note"
          type="textarea"
          :label="false"
          :required="false"
          :placeholder="$t('orders.details.commentsTextBoxLabel')"
        />

        <Button
          mode="ghost"
          size="s"
          type="box"
          icon="send"
          class="text-main hover:text-main-80"
          :pending="pending"
          :disabled="!note"
        />

      </form>

    </Guard>

  </div>

</template>
