<script setup lang="ts">

import { getDocuments } from '@/modules/profile/store'
import { cookieConsent } from '@/store'
import { onMounted, reactive, ref } from 'vue'
import { userFirstName, userLastName, userMail, userName } from '@/modules/auth/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Card from '@/modules/profile/components/Card.vue'
import Modal from '@lib/components/blocks/Modal.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Clarity from '@microsoft/clarity'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'

import type { DocumentModel, DocumentRouteParams } from '@/modules/profile/types'

const pending           = ref<boolean>( true )
const invoices          = ref<DocumentModel[]>( null )
const contracts         = ref<DocumentModel[]>( null )
const openPreferences   = ref<boolean>( false )
const necessaryCookies  = reactive({ enabled: true, expanded: false })
const analyticsCookies  = reactive({ enabled: cookieConsent.value, expanded: false })

function savePreferences() {

  if ( analyticsCookies.enabled ) {

    cookieConsent.value = true
    localStorage.setItem( 'cookie-consent', 'true' )

    Clarity.consent()

  }

  else {

    cookieConsent.value = false
    localStorage.setItem( 'cookie-consent', 'false' )

    Clarity.consent( false )

  }

  openPreferences.value = false

}

async function getProfileData() {

  const [ invoicesPayload, contractsPayload ] = await Promise.all( [
    getDocuments<DocumentRouteParams>({ documentType: 'invoice' }),
    getDocuments<DocumentRouteParams>({ documentType: 'contract' })
  ] )

  invoices.value = invoicesPayload.payload?.documents ?? null
  contracts.value = contractsPayload.payload?.documents ?? null

  pending.value = false

}

onMounted(() => getProfileData())

</script>

<template>

  <div class="w-full h-full bg-core-30 overflow-hidden">

    <div class="max-h-full max-w-7xl mx-auto p-2 md:p-10 pt-16 md:pt-24 grid xl:grid-cols-[max-content_1fr] items-start gap-2 md:gap-6 overflow-y-auto">

      <div class="xl:sticky xl:top-0 xl:min-w-[24rem] bg-core-10 rounded-xs">

        <div class="pb-6 grid grid-rows-[max-content_max-content_max-content] place-items-center border-b border-core-30">

          <div class="h-16 relative">

            <div class="w-24 h-24 absolute -top-12 left-1/2 -translate-x-1/2 grid place-content-center rounded-full bg-core-120">
              <Icon name="profile" size="l" class="text-core-10" />
            </div>

          </div>

          <p class="text-2xl font-medium">
            {{ userName }}
          </p>

          <p class="text-xs tracking-wider uppercase text-core">
            {{ $t('profile.header') }}
          </p>

        </div>

        <InputField
          :label="$t('profile.label.contactName')"
          :model-value="`${userFirstName} ${userLastName}`"
          :readonly="true"
          :required="false"
        />

        <InputField
          :label="$t('profile.label.email')"
          :model-value="userMail"
          :readonly="true"
          :required="false"
        />

        <Button
          mode="ghost"
          class="w-full"
          @click="openPreferences = true"
        >
          {{ $t('profile.label.cookies') }}
        </Button>

      </div>

      <div class="w-full grid grid-rows-[max-content_max-content] items-start gap-2 md:gap-6">

        <div class="bg-core-10 rounded-xs">

          <div class="w-full h-16 px-4 flex items-center space-x-4 border-b border-core-30">

            <Icon name="portal-invoice" class="text-main" size="l" />

            <p class="font-medium grow">
              {{ $t('profile.billingHistory.header') }}
            </p>

            <Button
              mode="ghost"
              @click="$router.push({ name: 'Documents', params: { documentType: 'billing-history' } })"
            >
              {{ $t('profile.viewAllLabel') }}
            </Button>

          </div>

          <Transition name="view" mode="out-in">

            <div v-if="pending" class="flex items-center space-x-4 justify-center p-10">
              <Loader :name="$t('profile.billingHistory.loaderName')" />
            </div>

            <div v-else-if="!invoices" class="w-full py-10 flex items-center justify-center space-x-2">

              <Icon name="folder" class="text-main" />
              <p class="text-sm">
                {{ $t('global.phrase.noRecordsFound', { name: $t('profile.documents.billing.name') }) }}
              </p>

            </div>

            <div v-else class="max-h-[30rem] grid md:grid-cols-2 gap-4 p-4 overflow-y-auto">

              <Card v-for="invoice in invoices" :key="invoice.id" :invoice="invoice" />

            </div>

          </Transition>

        </div>

        <div class="bg-core-10 rounded-xs">

          <div class="bg-core-10 rounded-xs">

            <div class="w-full h-16 px-4 flex items-center space-x-4 border-b border-core-30">

              <Icon name="portal-contract" class="text-main" size="l" />

              <p class="font-medium grow">
                {{ $t('profile.contracts.header') }}
              </p>

              <Button
                mode="ghost"
                @click="$router.push({ name: 'Documents', params: { documentType: 'contracts' } })"
              >
                {{ $t('profile.viewAllLabel') }}
              </Button>

            </div>

          </div>

          <Transition name="view" mode="out-in">

            <div v-if="pending" class="flex items-center space-x-4 justify-center p-10">
              <Loader :name="$t('profile.contracts.loaderName')" />
            </div>

            <div v-else-if="!contracts" class="w-full py-10 flex items-center justify-center space-x-2">

              <Icon name="folder" class="text-main" />
              <p class="text-sm">
                {{ $t('global.phrase.noRecordsFound', { name: $t('profile.documents.contracts.name') }) }}
              </p>

            </div>

            <div v-else class="grid md:grid-cols-2 gap-4 p-4">

              <Card v-for="contract in contracts" :key="contract.id" :invoice="contract" />

            </div>

          </Transition>

        </div>

      </div>

    </div>

    <Modal v-if="$route.name === 'Documents'">
      <div class="w-full h-full flex items-center justify-center">
        <RouterView />
      </div>
    </Modal>

    <Sidebar
      :dim="true"
      :open="openPreferences"
      :strict="true"
      position="left"
      @close="openPreferences = false"
    >

      <div class="w-full md:w-[34rem] transition-[width] h-full grid grid-rows-[max-content_1fr_max-content] bg-core-10 overflow-hidden">

        <div class="w-full h-14 grid grid-cols-[1fr_max-content] items-center border-b border-core-30">

          <p class="font-medium px-4">
            {{ $t('profile.cookies.header') }}
          </p>

          <div class="w-14 h-full grid place-content-center border-l border-core-30">

            <Button
              type="box"
              mode="ghost"
              icon="close"
              @click="openPreferences = false"
            />

          </div>

        </div>

        <div class="p-4 grid gap-4 content-start overflow-y-auto">

          <div class="p-4 grid gap-2">

            <p class="font-medium">
              {{ $t('profile.cookies.usage.title') }}
            </p>

            <p class="text-sm">
              {{ $t('profile.cookies.usage.content.p1') }}
              <br>
              <br>
              {{ $t('profile.cookies.usage.content.p2') }}
            </p>

          </div>

          <div class="border border-core-30">

            <div class="w-full h-auto p-2 flex items-center space-x-2 cursor-pointer" @click="necessaryCookies.expanded = !necessaryCookies.expanded">

              <Button
                size="s"
                type="box"
                mode="ghost"
                :icon="necessaryCookies.expanded ? 'chevron-up' : 'chevron-down'"
                @click.stop="necessaryCookies.expanded = !necessaryCookies.expanded"
              />

              <p class="text-sm md:text-base font-medium grow">
                {{ $t('profile.cookies.necessary.title') }}
              </p>

              <p class="text-xs text-white px-2 py-0.5 bg-warning rounded-xs">
                {{ $t('profile.cookies.necessary.label') }}
              </p>

              <Checkbox v-model="necessaryCookies.enabled" :disabled="true" class="z-50" />

            </div>

            <div v-if="necessaryCookies.expanded" class="p-4 border-t border-core-30 bg-core-20">
              <p class="text-sm">
                {{ $t('profile.cookies.necessary.content') }}
              </p>
            </div>

          </div>

          <div class="border border-core-30">

            <div class="w-full h-auto p-2 flex items-center space-x-2 cursor-pointer" @click="analyticsCookies.expanded = !analyticsCookies.expanded">

              <Button
                size="s"
                type="box"
                mode="ghost"
                :icon="analyticsCookies.expanded ? 'chevron-up' : 'chevron-down'"
                @click.stop="analyticsCookies.expanded = !analyticsCookies.expanded"
              />

              <p class="text-sm md:text-base font-medium grow">
                {{ $t('profile.cookies.analytics.title') }}
              </p>

              <Checkbox v-model="analyticsCookies.enabled" class="z-50" />

            </div>

            <div v-if="analyticsCookies.expanded" class="p-4 border-t border-core-30 bg-core-20">
              <p class="text-sm">
                {{ $t('profile.cookies.analytics.content.p1') }}
                <br>
                <br>
                {{ $t('profile.cookies.analytics.content.p2') }}
                <br>
                <br>
                {{ $t('profile.cookies.analytics.content.p3') }}
                <a class="font-medium text-main" target="_blank" href="https://privacy.microsoft.com/en-US/privacystatement">{{ $t('profile.cookies.analytics.content.linkLabel') }}</a>.
              </p>
            </div>

          </div>

          <div class="p-4 grid gap-2 border border-core-30 bg-core-20">

            <p class="font-medium">
              {{ $t('profile.cookies.moreInfo.title') }}
            </p>

            <p class="text-sm">
              {{ $t('profile.cookies.moreInfo.content') }}
              <span class="text-main font-medium">{{ $t('profile.cookies.moreInfo.linkLabel') }}</span>.
            </p>

          </div>

        </div>

        <div class="w-full grid">

          <Button
            size="l"
            @click="savePreferences"
          >
            {{ $t('profile.cookies.actionButtons.save') }}
          </Button>

        </div>

      </div>

    </Sidebar>

  </div>

</template>
