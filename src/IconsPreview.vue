<script setup lang="ts">

import { iconsList } from '@lib/store/icon'
import { searchModel } from '@lib/scripts/utils'
import { computed, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

const icons         = computed(() => iconsList( 'currentColor' ))
const searchQuery   = ref( '' )
const filteredIcons = computed(() => searchModel( icons.value, searchQuery.value, 'name' ))

</script>

<template>

  <div class="md:p-10 bg-background">

    <div
      class="font-medium w-full sticky top-0 h-10 px-4 grid grid-cols-[1fr_2.5rem_2.5rem_2.5rem_2.5rem] gap-x-2 items-center bg-field-01 border-b border-border-subtle-00"
    >

      <div class="flex items-center space-x-3">

        <p class="whitespace-nowrap">
          Name
        </p>

        <InputField
          v-model="searchQuery"
          size="m"
          label="Search Icons"
          class="w-full font-normal"
          :required="false"
          placeholder="eg: chevron-down"
        >

          <template #prefix>

            <div class="pl-3">
              <Icon name="search" size="m" />
            </div>

          </template>

        </InputField>

      </div>

      <div class="h-full grid place-content-center">
        <p>s</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>m</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>l</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>xl</p>
      </div>

    </div>

    <div
      v-for="icon in filteredIcons" :key="String(icon.name)"
      class="text-sm w-full h-14 px-4 grid grid-cols-[1fr_2.5rem_2.5rem_2.5rem_2.5rem] gap-x-2 items-center border-b border-border-subtle-00"
    >

      <p>{{ icon.name }}</p>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="s" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="m" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="l" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="xl" />
      </div>

    </div>

  </div>

</template>
