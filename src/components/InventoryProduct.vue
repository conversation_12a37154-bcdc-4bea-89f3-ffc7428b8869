<script setup lang="ts">

import { computed } from 'vue'
import { checkValue } from '@lib/scripts/utils'
import { productDetailsOptions } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'
import SimpleBadge from '@/components/SimpleBadge.vue'

import type { InventoryPanelProduct } from '@/modules/inventory/types'

const props = withDefaults( defineProps<{

  item:              InventoryPanelProduct
  locked?:           boolean
  itemPrice?:        number
  addPending?:       boolean
  isSelected?:       boolean
  itemQuantity:      number
  allowKitItems:     boolean
  allowPriceUpdate?: boolean

  action?:         ( product: InventoryPanelProduct ) => ( Promise<InventoryPanelProduct | void> | void )
  editItemAction?: ( product: InventoryPanelProduct ) => ( Promise<InventoryPanelProduct | void> | void )

}>(), {

  action: ( product: InventoryPanelProduct ) => {

    if ( !product?.inventoryId )
      return

    productDetailsOptions.id = product?.inventoryId
    productDetailsOptions.type = 'Live'

  }

})

const emits = defineEmits<{
  ( event: 'addProduct', data: InventoryPanelProduct ): void
  ( event: 'removeProduct' ): void
  ( event: 'updateProduct' ): void
}>()

const price                 = defineModel<number>( 'itemPrice' )
const product               = defineModel<InventoryPanelProduct>( 'item' )
const quantity              = defineModel<number>( 'itemQuantity' )

const isValidProduct        = computed(() => props.allowPriceUpdate ? checkValue( price.value ) && quantity.value : quantity.value )
const enablePriceInput      = computed(() => props.allowPriceUpdate && props.isSelected )
const disableKitProductAdd  = computed(() => !props.allowKitItems && !!props.item.kitComponents )

/**
 * Adds an item to the selected items.
 *
 * @param {BaseProductWithDeclaredValue} product - The product to add.
 */

function addItem( product: InventoryPanelProduct ) {
  emits( 'addProduct', product )
}

/**
 * Adds quantity to the selected product.
 */

function addQuantity() {
  quantity.value++
  emits( 'updateProduct' )
}

/**
 * Removes quantity from the selected product.
 */

function removeQuantity() {

  if ( quantity.value > 1 ) {
    quantity.value--
    emits( 'updateProduct' )
  }

  else {
    emits( 'removeProduct' )
  }

}

defineExpose({
  product,
  quantity,
  enablePriceInput
})

</script>

<template>

  <div class="border-b border-border-subtle-00">

    <Component
      :is="action && !!product?.inventoryId ? 'button' : 'div'"
      data-element="container"
      class="w-full h-24 relative flex"
      :class="{
        'cursor-pointer hover:bg-core-120/[0.03]': action && !!product?.inventoryId,
      }"
      @click="() => action ? action(product) : null"
    >

      <div class="w-full h-full grid">

        <div class="row-span-2 grid grid-cols-[1fr_max-content]">

          <div class="truncate h-full px-4 grid content-center">

            <Button
              v-if="isSelected && editItemAction"
              size="auto"
              mode="ghost"
              class="text-sm font-medium truncate text-left flex items-center gap-2 md:hidden"
              data-button="edit"
              :class="{
                'text-error': !isValidProduct,
                'text-main': isValidProduct,
              }"
              @click.stop.prevent="() => editItemAction ? editItemAction(item) : null"
            >

              <p data-element="edit-sku" class="truncate">
                {{ product?.sku }}
              </p>
              <Icon class="min-w-3" name="edit" size="s" />

            </Button>

            <p
              data-element="sku"
              class="text-sm font-medium truncate text-left"
              :class="{
                'text-error': !isValidProduct,
                'hidden md:inline': isSelected && editItemAction,
              }"
            >
              {{ product?.sku }}
            </p>

            <p data-element="title" class="truncate text-xs text-core text-left">
              {{ product?.title }}
            </p>

          </div>

          <div
            v-if="isSelected"
            class="pr-2 grid items-center justify-end"
          >

            <div class="flex items-center">

              <Button
                size="s"
                type="badge"
                icon="minus"
                mode="tertiary"
                icon-size="s"
                data-button="remove-qty"
                @click.stop.prevent="removeQuantity()"
              />

              <InputField
                v-model="quantity"
                data-input="qty"
                size="s"
                type="number"
                label="Qty"
                class="w-[5.5rem] min-w-[5.5rem] max-w-[5.5rem]"
                :required="false"
                :validation-options="{ min: 1 }"
                placeholder="Qty"
                :show-controls="false"
                @update:model-value="emits('updateProduct')"
                @click.stop.prevent
              />

              <Button
                size="s"
                icon="add"
                type="badge"
                mode="tertiary"
                icon-size="s"
                data-button="add-qty"
                @click.stop.prevent="addQuantity()"
              />

            </div>

          </div>

          <div v-else class="pr-2 grid items-center justify-end">

            <SimpleBadge v-if="disableKitProductAdd" data-element="not-kit-allowed">
              KIT
            </SimpleBadge>

            <Button
              v-else-if="!locked"
              size="s"
              type="pill"
              mode="tertiary"
              class="text-sm self-center"
              :pending="addPending"
              data-button="add-item"
              @click.stop="() => addItem(product)"
            >
              Add
            </Button>

            <div v-else class="px-2 text-main">
              <Icon name="locked" size="s" />
            </div>

          </div>

        </div>

        <div class="w-full h-4 px-4 flex items-center space-x-4">

          <p data-element="product-id" class="text-sm text-core text-left">
            {{ product?.id }}
          </p>

          <p class="text-sm grow text-left">
            <span v-if="locked" class="text-core bg-core-40 px-2 py-0.5 rounded-sm">Not Available</span>
            <span v-else-if="checkValue(product?.availableQuantity) && !locked && !product.kitComponents" data-element="stock" class="text-core">Stock: {{ product?.availableQuantity }}</span>
            <SimpleBadge v-else-if="!disableKitProductAdd && product.kitComponents" data-element="kit-badge">
              KIT
            </SimpleBadge>
          </p>

          <InputField
            v-if="allowPriceUpdate ? true : checkValue(price)"
            v-model="price"
            size="s"
            type="currency"
            label="Price"
            class="w-[7rem] min-w-[7rem] max-w-[7rem]"
            :required="false"
            :readonly="!enablePriceInput"
            data-input="edit-price"
            @input="emits('updateProduct')"
            @click.stop.prevent
          />

        </div>

      </div>

    </Component>

  </div>

</template>
