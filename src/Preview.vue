<script setup lang="ts">

import { ref } from 'vue'

import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import InputField from '@lib/components/inputs/input/InputField.vue'

const textInput = ref<string>( null )
const textInputReadonly = ref<string>( 'Readonly text' )
const numberInput = ref<number>( null )
const numberInputWithControls = ref<number>( null )
const emailInput = ref<string>( null )
const passwordInput = ref<string>( 'password' )
const selectInput = ref<string>( null )
const multiSelectInput = ref<string[]>( [] )
const textareaInput = ref<string>( null )

const options: DropListOption[] = [
  { id: 1, name: 'Option 1' },
  { id: 2, name: 'Option 2' },
  { id: 3, name: 'Option 3' },
  { id: 4, name: 'Option 4' },
  { id: 5, name: 'Option 5' },
  { id: 6, name: 'Option 6' },
  { id: 7, name: 'Option 7' },
  { id: 8, name: 'Option 8' },
  { id: 9, name: 'Option 9' },
  { id: 10, name: 'Option 10' },
  { id: 11, name: 'Option 11' },
  { id: 12, name: 'Option 12' },
  { id: 13, name: 'Option 13' },
  { id: 14, name: 'Option 14' },
  { id: 15, name: 'Option 15' },
  { id: 16, name: 'Option 16' },
  { id: 17, name: 'Option 17' },
  { id: 18, name: 'Option 18' },
  { id: 19, name: 'Option 19' },
  { id: 20, name: 'Option 20' }
]

</script>

<template>

  <div class="bg-background">

    <div class="w-full h-12 px-4 flex items-center border-b border-border-subtle-00">
      <h1 class="text-lg font-bold">
        Preview
      </h1>
    </div>

    <form class="p-4 grid md:grid-cols-3 gap-3">

      <InputField v-model="textInput" type="text" label="Text Input" message="Text helper message" />

      <InputField v-model="textInput" type="text" label="Text Input ( Disabled )" message="Text helper message" :disabled="true" />

      <InputField v-model="textInputReadonly" type="text" label="Text Input ( Readonly )" message="Text helper message" :readonly="true" />

      <InputField
        v-model="numberInputWithControls"
        type="number"
        :step="5"
        label="Number Input ( controls )"
        status="warning"
        message="Warning Message"
        :validation-options="{ min: 0, max: 10 }"
      />

      <InputField v-model="numberInput" type="number" label="Number Input ( Strict )" :strict="true" :show-controls="false" />

      <InputField v-model="emailInput" type="email" label="Email Input" />

      <InputField v-model="passwordInput" type="password" label="Password Input" />

      <InputField v-model="selectInput" type="select" label="Select Input" :options="options" />

      <InputField v-model="multiSelectInput" type="select" :label="false" :multiple="true" :options="options" />

      <InputField v-model="multiSelectInput" type="select" label="Multi Select Input" :multiple="true" :options="options" />

      <InputField v-model="multiSelectInput" type="select" label="Multi Select Input ( Disabled )" :multiple="true" :options="options" :disabled="true" />

      <InputField v-model="multiSelectInput" type="select" label="Multi Select Input ( Warning )" :multiple="true" :options="options" status="warning" message="Warning Message" />

      <InputField v-model="textareaInput" type="textarea" label="Textarea Input" class="md:col-span-3" :required="false" />

      <Checkbox label="Checkbox Input" />
      <Checkbox label="Checkbox Input" :disabled="true" />
      <Checkbox label="Radio Checkbox" :radio="true" />

      <InputField v-model="textInput" type="text" label="Text Input ( Large )" />
      <InputField v-model="textInput" type="text" label="Text Input ( Medium )" size="m" />
      <InputField v-model="textInput" type="text" label="Text Input ( Small )" size="s" />

      <InputField v-model="multiSelectInput" type="select" label="Multi ( Large )" :multiple="true" :options="options" />
      <InputField v-model="multiSelectInput" type="select" label="Multi ( Medium )" :multiple="true" :options="options" size="m" />
      <InputField v-model="multiSelectInput" type="select" label="Multi ( Small )" :multiple="true" :options="options" size="s" />
    </form>

  </div>

</template>
