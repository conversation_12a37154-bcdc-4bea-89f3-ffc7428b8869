<script setup lang="ts">

import { onMounted } from 'vue'
import { setTheme, theme } from '@lib/scripts/themeUtils'

import Alert from '@lib/components/blocks/alert/Alert.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Confirm from '@lib/components/blocks/Confirm.vue'
import Notification from '@lib/components/blocks/notification/Notification.vue'

onMounted(() => {

  // Set the theme based on the user's preference when the app is mounted.

  setTheme( theme.value )

  // Add default-design-system wrapper class to the document element.
  // The default wrapper is used to set the default styles for the entire design system.
  // We can re-style the design system by applying another wrapper with different styles.
  // Eg: document.documentElement.classList.add( 'my-custom-design-system' ).

  document.documentElement.classList.add( 'default-design-system' )

})

</script>

<template>

  <main class="w-full h-full bg-background relative overflow-hidden">

    <RouterView v-slot="{ Component }">

      <Transition mode="out-in" name="route">

        <Component :is="Component" />

      </Transition>

    </RouterView>

    <div class="fixed w-full h-full flex items-center justify-center space-x-2">
      <Loader name="" />
    </div>

    <Alert />
    <Confirm />
    <Notification />

  </main>

</template>
