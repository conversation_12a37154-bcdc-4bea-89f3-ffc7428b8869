import { asnRoutes } from '@/modules/asn/routes'
import { ordersRoutes } from '@/modules/orders/routes'
import { createRoutes } from '@lib/scripts/routerUtils'
import { registerGuard } from '@lib/auth/scripts/guard'
import { profileRoutes } from '@/modules/profile/routes'
import { inventoryRoutes } from '@/modules/inventory/routes'
import { dashboardRoutes } from '@/modules/dashboard/routes'
import { documentationRoutes } from '@/modules/documentation/routes'
import { accessGuard, scopesProvider } from '@/plugins/guard'
import { createRouter, createWebHistory } from 'vue-router'

import type { GuardScope, Scope } from '@/types'

const router = createRouter({

  history: createWebHistory( import.meta.env.BASE_URL ),

  routes: createRoutes<GuardScope>( [
    {
      name:      'App',
      path:      '/',
      redirect:  '/dashboard',
      component: () => import( '@/modules/app/views/App.vue' ),
      meta:      { requiresAuth: true },
      children:  [
        {
          name:      'Icons',
          path:      '/icons',
          meta:      { requiresAuth: true, scope: 'safe-route' },
          component: () => import( '@/IconsPreview.vue' )
        },
        {
          name:      'Preview',
          path:      '/preview',
          meta:      { requiresAuth: true, scope: 'safe-route' },
          component: () => import( '@/Preview.vue' )
        },
        dashboardRoutes,
        ordersRoutes,
        asnRoutes,
        profileRoutes,
        inventoryRoutes,
        documentationRoutes
      ]
    }
  ] )

})

registerGuard<Scope, GuardScope>( router, accessGuard, scopesProvider )

export default router
