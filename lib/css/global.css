@import 'tailwindcss';
@import '@lib/css/config.css';
@import '@lib/css/vueTransitions.css' layer(base);
@import '@lib/components/button/Button.css' layer(base);
@import '@lib/components/blocks/tag/Tag.css' layer(base);
@import '@lib/components/buttons/tab/Tab.css' layer(base);
@import '@lib/components/blocks/alert/Alert.css' layer(base);
@import '@lib/components/inputs/input/InputField.css' layer(base);
@import '@lib/components/utils/snackbar/Snackbar.css' layer(base);
@import '@lib/components/utils/dropdown/Dropdown.css' layer(base);
@import '@lib/components/inputs/checkbox/Checkbox.css' layer(base);
@import '@lib/components/blocks/status-badge/StatusBadge.css' layer(base);
@import '@lib/components/blocks/notification/Notification.css' layer(base);
@import '@lib/components/blocks/content-switcher/ContentSwitcher.css' layer(base);

@layer base {

  /* ---- SETUP */

  /*
    The default border color has changed to `currentColor` in Tailwind CSS v4,
    so we've added these compatibility styles to make sure everything still
    looks the same as it did with Tailwind CSS v3.

    If we ever want to remove these styles, we need to add an explicit border
    color utility to any element that depends on these defaults.
  */

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  *::selection { @apply bg-main-60/40 }

  /* Scrollbar Styles */

  ::-webkit-scrollbar { width: 6px; height: 6px }
  ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 20px var(--scrollbar-track) }
  ::-webkit-scrollbar-thumb { -webkit-box-shadow: inset 0 0 50px var(--scrollbar-thumb) }
  ::-webkit-scrollbar-thumb:hover { -webkit-box-shadow: inset 0 0 50px var(--scrollbar-thumb) }

  /*
    Set the html and body height to 100%.

    This is necessary to ensure the #app element is the overflow container,
    and the mobile styles work as expected.

    We also set the text color to the primary text color.
  */

  html, body { @apply h-full text-text-primary; }

  /* Set the App container to full height, and make it the main overflow container. */

  #app { @apply h-full overflow-y-auto bg-background isolate; }

  /*
    The old theme colors. TODO: ( Will be removed after the migration to the new theme colors is complete ).
    The new colors are defined in the `@lib/css/themes/default.css` file.
  */

  :root {

    --main-l-10:          rgb(255 255 255);
    --main-l-20:          rgb(231 236 255);
    --main-l-30:          rgb(211 221 253);
    --main-l-40:          rgb(167 187 251);
    --main-l-50:          rgb(123 154 250);
    --main-l-60:          rgb(79 120 248);
    --main-l-70:          rgb(35 86 246);
    --main-l-80:          rgb(28 69 197);
    --main-l-90:          rgb(21 52 148);
    --main-l-100:         rgb(14 34 98);
    --main-l-110:         rgb(7 17 49);
    --main-l-120:         rgb(3 7 22);

    --core-l-10:          rgb(255 255 255);
    --core-l-20:          rgb(249 249 249);
    --core-l-30:          rgb(232 232 232);
    --core-l-40:          rgb(220 220 220);
    --core-l-50:          rgb(202 202 202);
    --core-l-60:          rgb(163 163 163);
    --core-l-70:          rgb(102 102 102);
    --core-l-80:          rgb(82 82 82);
    --core-l-90:          rgb(61 61 61);
    --core-l-100:         rgb(41 41 41);
    --core-l-110:         rgb(20 20 20);
    --core-l-120:         rgb(0 0 0);

    --data1-l-120:        rgb(255 199 0);
    --data1-l-110:        rgb(255 244 204);
    --data1-l-100:        rgb(255 249 229);

    --data2-l-120:        rgb(37 191 155);
    --data2-l-110:        rgb(205 247 237);
    --data2-l-100:        rgb(230 251 246);

    --data3-l-120:        rgb(0 65 141);
    --data3-l-110:        rgb(204 230 255);
    --data3-l-100:        rgb(229 243 255);

    --data4-l-120:        rgb(255 0 0);
    --data4-l-110:        rgb(255 204 204);
    --data4-l-100:        rgb(255 229 229);

    --error-l:            rgb(255 67 67);
    --error-l-30:         rgb(249 218 218);
    --error-l-20:         rgb(252 237 236);
 
    --warning-l:          rgb(239 138 67);
    --warning-l-30:       rgb(249 233 219);
    --warning-l-20:       rgb(251 243 237);

    --success-l:          rgb(79 159 82);
    --success-l-30:       rgb(223 235 221);
    --success-l-20:       rgb(238 245 238);

    --owd-l-120:          rgb(251 102 40);

    --selection-l:        rgba(35, 86, 246, 0.1);

    --scrollbar-track-l:  rgb(240, 240, 240);
    --scrollbar-thumb-l:  rgb(232, 232, 232);

  }

  html {

    --selection: var(--selection-l); /* rgb(35, 86, 246, 0.1) */

    --scrollbar-track: var(--scrollbar-track-l); /* rgb(240 240 240) */
    --scrollbar-thumb: var(--scrollbar-thumb-l); /* rgb(232 232 232) */

  }

}