<script setup lang="ts">

import { ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'

interface Props {
  size?:     'l' | 'm' | 's'
  index:     number
  total:     number
  option:    DropListOption
  multiple?: boolean
  selected?: boolean
  disabled?: boolean
}

defineOptions({ name: 'DropdownItem' })

const props = withDefaults( defineProps<Props>(), { size: 'l' })
const emits = defineEmits<{ close: [], select: [option: DropListOption], pending: [isPending: boolean] }>()

const error   = ref<string>( null ) // -------------------- Stores the error from the action from the option.
const pending = ref<boolean>( false ) // ------------------ Indicates if the action from the option is pending.

/**
 * Handle the action defined for the dropdown option.
 * This function executes the action associated with the dropdown option and manages the state of the dropdown.
 * If there is no action defined, or the item is selected, it does nothing.
 */
async function handleOptionAction() {

  if ( !props.option?.action || props.selected ) { // ---- If there is no action defined for the option,

    emits( 'select', props.option ) // ------------------- select the option and

    if ( !props.multiple && !props.selected ) // --------- If the dropdown is not multi-select type and the option is deselected,
      emits( 'close' ) // -------------------------------- close the dropdown.

    return

  }

  pending.value = true
  emits( 'pending', true ) // ---------------------------- Emit the pending state.

  const actionData = await props.option.action() // ------ Execute the action defined for the option.

  error.value = actionData?.error ?? null // ------------- If the action returns an error, set it to error.

  if ( !error.value ) // --------------------------------- If there is no error,
    emits( 'select', props.option ) // ------------------- select the option and

  pending.value = false
  emits( 'pending', false ) // --------------------------- Emit the pending state.

  if ( !error.value && !props.multiple ) // -------------- If the dropdown is not multi-select type and there is no error,
    emits( 'close' ) // ---------------------------------- close the dropdown.

}

</script>

<template>

  <button
    class="dropdown-option"
    :name="`option-${index}`"
    :class="{

      's': size === 's',
      'm': size === 'm',
      'l': size === 'l',

      'option-has-error': error,
      'option-is-pending': pending,
      'option-is-selected': selected,
      'option-is-multiple': multiple,
      'option-has-description': !!option?.description,

    }"
    :disabled="disabled"
    @click.stop="handleOptionAction"
    @keydown.space.prevent="handleOptionAction"
    @keydown.enter.prevent="handleOptionAction"
  >

    <slot>

      <div class="dropdown-option-content">

        <div v-if="multiple" class="dropdown-option-checkbox">
          <Icon v-if="pending" name="loading" />
          <Checkbox v-else :model-value="selected" :readonly="true" />
        </div>

        <div class="dropdown-option-text">

          <p class="dropdown-option-name">
            {{ option.name }}
          </p>

          <p class="dropdown-option-description">
            {{ error ?? option.description }}
          </p>

        </div>

        <div v-if="!multiple && (error || pending || selected)" class="dropdown-option-decorator">

          <Icon v-if="error" name="issue-circle" size="m" />
          <Icon v-else-if="pending" name="loading" />
          <Icon v-else name="checkmark" size="m" />

        </div>

      </div>

    </slot>

  </button>

</template>
