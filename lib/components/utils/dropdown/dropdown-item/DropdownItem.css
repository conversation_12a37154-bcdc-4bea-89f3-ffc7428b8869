.default-design-system {

  .dropdown-option {

    @apply 
    text-left relative w-full bg-layer-01 cursor-pointer overflow-hidden
    hover:bg-layer-hover-01 hover:before:border-t-0 hover:[&+*]:before:border-t-0
    before:absolute before:w-[calc(100%-2rem)] before:h-full before:top-0 before:left-4 before:border-t first-of-type:before:border-t-0 before:border-border-subtle-00
    focus-visible:before:w-full focus-visible:before:left-0 focus-visible:outline focus-visible:outline-focus focus-visible:[&+*]:before:border-t-0 focus-visible:-outline-offset-1 focus-visible:before:border-t-0;

    &.option-is-selected {

      @apply 
      bg-layer-selected-01
      hover:bg-layer-selected-hover-01
      before:border-t-0 [&+*]:before:border-t-0;

    }

    &.s { @apply h-8; }
    &.m { @apply h-10; }
    &.l { @apply h-12; }

    &.option-is-pending {
      @apply pointer-events-none;
    }

    &:disabled {
      @apply pointer-events-none opacity-50;
    }

    .dropdown-option-content {

      @apply truncate w-full h-full grid grid-cols-[1fr_max-content] items-center;

      .dropdown-option-text {

        @apply truncate h-full px-4 grow grid grid-cols-1 grid-rows-1 items-center;

        .dropdown-option-name {
          @apply truncate text-sm;
        }

        .dropdown-option-description {
          @apply truncate text-xs text-text-helper;
        }

      }

      .dropdown-option-decorator {
        @apply pr-4;
      }

    }

    &.option-is-multiple {

      .dropdown-option-content {

        @apply grid-cols-[max-content_1fr];

        .dropdown-option-checkbox {
          @apply pl-4 grid place-content-center;
        }

        .dropdown-option-text {
          @apply pl-3 pr-4;
        }

      }

    }

    &.option-has-description, &.option-has-error {

      &.s { @apply h-12; }
      &.m { @apply h-12; }

      .dropdown-option-content {

        .dropdown-option-text {

          @apply grid-rows-[1.3fr_1fr] items-start;

          .dropdown-option-name {
            @apply self-end;
          }

        }

      }

    }

    &.option-has-error {

      .dropdown-option-content {

        .dropdown-option-text {

          .dropdown-option-description {
            @apply text-text-error;
          }

        }

        .dropdown-option-decorator {
          @apply text-text-error;
        }

      }

    }

  }

}