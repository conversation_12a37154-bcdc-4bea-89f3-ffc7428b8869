import { ref } from 'vue'
import { useBreakpoint } from '@lib/scripts/utils'

/**
 * Sets up a dropdown element based on the target element's position and the current viewport size.
 *
 * @param {HTMLDivElement} dropdownElement - The dropdown element to be positioned.
 * @param {HTMLDivElement | HTMLButtonElement} targetElement - The element that the dropdown is associated with.
 */
export function setupDropdown( dropdownElement: HTMLDivElement, targetElement: HTMLDivElement | HTMLButtonElement, maxW: number = 240 ) {

  if ( !dropdownElement ) {
    console.warn( 'setupDropdown: The dropdown element is not defined.' )
    return
  }

  if ( !targetElement ) {
    console.warn( 'setupDropdown: The target element is not defined.' )
    return
  }

  let isDesktop  = useBreakpoint( 'md' ) // --------------------------------------------------- Check if the current breakpoint is 'md' or larger.

  const dropDown = dropdownElement.style // --------------------------------------------------- Get the style of the dropdown element.
  const position = ref<'top' | 'bottom'>( 'bottom' ) // --------------------------------------- Default position of the dropdown element.

  if ( !isDesktop ) { // ---------------------------------------------------------------------- If screen is not a desktop, the dropdown will be set for smaller screens in the component styles.

    dropDown.width = '100%' // ---------------------------------------------------------------- Set the width of the dropdown element to 100%.
    dropDown.bottom = '0px' // ---------------------------------------------------------------- Set the bottom position of the dropdown element to 0.

    return

  }

  const maxH = 320 // ------------------------------------------------------------------------- Maximum height of the dropdown element.

  let docW = document.documentElement.clientWidth // ------------------------------------------ Get the document width.
  let docH = document.documentElement.clientHeight // ----------------------------------------- Get the document height.

  /**
   * Resizes the dropdown element based on the target element's position and the current viewport size.
   * This function calculates the available space above and below the target element,
   * and positions the dropdown accordingly. It also sets the width and height of the dropdown
   * element based on the target element's dimensions and the minimum and maximum constraints.
   */
  function resizeDropdown() {

    dropdownElement.setAttribute( 'style', '' ) // -------------------------------------------- Clear the style of the dropdown element.

    isDesktop = useBreakpoint( 'md' ) // ------------------------------------------------------ Check if the current breakpoint is 'md' or larger.

    if ( !isDesktop ) { // -------------------------------------------------------------------- If screen is not a desktop, the dropdown will be set for smaller screens in the component styles.

      dropDown.width = '100%' // -------------------------------------------------------------- Set the width of the dropdown element to 100%.
      dropDown.bottom = '0px' // -------------------------------------------------------------- Set the bottom position of the dropdown element to 0.

      return

    }

    docW = document.documentElement.clientWidth // -------------------------------------------- Get the document width.
    docH = document.documentElement.clientHeight // ------------------------------------------- Get the document height.

    const tRect = targetElement.getBoundingClientRect() // ------------------------------------ Get the bounding rectangle of the target element.
    const dRect = dropdownElement.getBoundingClientRect() // ---------------------------------- Get the bounding rectangle of the target element.

    let dropDownW = dRect.width //  ----------------------------------------------------------- Set the initial width of the dropdown element to it's self if bigger than minW.

    if ( dropDownW > maxW )
      dropDownW = maxW

    if ( tRect.width > dropDownW ) // --------------------------------------------------------- If the target element's width is greater than the minimum width and the dropdown's width, set the dropdown's width to the target element's width.
      dropDownW = tRect.width

    dropDown.width = `${dropDownW}px`

    const ttRemainder = tRect.top // ---------------------------------------------------------- Calculate the top remainder of the target element.
    const tlRemainder = tRect.left // --------------------------------------------------------- Calculate the left remainder of the target element.
    const tbRemainder = docH - ( tRect.top + tRect.height ) // -------------------------------- Calculate the bottom remainder of the target element.
    const trRemainder = docW - tRect.right // ------------------------------------------------- Calculate the right remainder of the target element.

    const dropDownH     = Math.min( maxH, docH - tRect.bottom ) // ---------------------------- Set the height of the dropdown element to the maximum height or the available space below the target element.
    const dropDownWDiff = ( dropDownW - tRect.width ) / 2 // ---------------------------------- Calculate the difference in width between the dropdown and target element.

    position.value = ttRemainder > tbRemainder && tbRemainder < maxH ? 'top' : 'bottom' // ---- Determine the position of the dropdown based on the available space above and below the target element.

    if ( position.value === 'top' ) { // ------------------------------------------------------ If there is more space above the target element than below, position the dropdown above it.
      dropDown.bottom = `${docH - tRect.top}px`
      dropDown.maxHeight = `${Math.min( maxH, ttRemainder )}px`
    }

    else { // --------------------------------------------------------------------------------- If there is more space below the target element, position the dropdown below it.
      dropDown.top = `${tRect.bottom}px`
      dropDown.maxHeight = `${dropDownH}px`
    }

    if ( docW < maxW ) { // ------------------------------------------------------------------- If the document width is less than the minimum width, set the dropdown width to the target width.
      dropDown.width = `${tRect.width}px`
    }

    if ( tlRemainder > dropDownWDiff && trRemainder > dropDownWDiff ) { // -------------------- If there is enough space on both sides of the target element, position the dropdown in the center.
      dropDown.left = `${tlRemainder - dropDownWDiff}px`
      dropDown.right = 'none'
    }

    else if ( tlRemainder > dropDownWDiff && trRemainder < dropDownWDiff ) { // --------------- If there is enough space on the left side, position the dropdown to the left of the target element.
      dropDown.left = 'none'
      dropDown.right = `${docW - tRect.right}px`
    }

    else if ( tlRemainder < dropDownWDiff && trRemainder > dropDownWDiff ) { // --------------- If there is enough space on the right side, position the dropdown to the right of the target element.s
      dropDown.left = `${tRect.left}px`
      dropDown.right = 'none'
    }

  }

  /**
   * Matches the scroll position of the dropdown with the target element.
   *
   * @param {Event} e - The scroll event object.
   */
  function matchScrollPosition( e: Event ) {

    const target      = e.target as HTMLDivElement // ----------------------------------------- Get the target element of the scroll event.
    const isDropdown  = target.getAttribute( 'data-dropdown' ) // ----------------------------- Check if the target element is a dropdown.

    if ( !isDropdown ) { // ------------------------------------------------------------------- If the target element is not a dropdown,

      if ( position.value === 'top' ) { // ---------------------------------------------------- If the dropdown is positioned at the top,

        const { top } = targetElement.getBoundingClientRect() // ------------------------------ Get the top position of the target element.
        dropDown.bottom = `${docH - top}px` // ------------------------------------------------ Set the bottom position of the dropdown to the document height minus the top position of the target element.

        return

      }

      const { bottom } = targetElement.getBoundingClientRect() // ----------------------------- Get the bottom position of the target element.
      dropDown.top = `${bottom}px` // --------------------------------------------------------- Set the top position of the dropdown to the bottom position of the target element.

    }

  }

  /**
   * Destroys the dropdown by removing event listeners.
   */
  function destroyDropdown() {
    window.removeEventListener( 'resize', resizeDropdown )
    document.removeEventListener( 'scroll', matchScrollPosition, true )
  }

  resizeDropdown()

  window.addEventListener( 'resize', resizeDropdown )
  document.addEventListener( 'scroll', matchScrollPosition, true )

  return destroyDropdown

}

/**
 * Focuses on the first option when the ArrowDown key is pressed, or when Tab is pressed and the droplist is open.
 *
 * @param {KeyboardEvent} event - The keyboard event object.
 * @param {HTMLDivElement | HTMLButtonElement} parentElement - The parent element.
 * @param {boolean} isDroplistOpen - Indicates if the droplist is open.
 * @returns { open: boolean } - If the ArrowDown key is pressed and the droplist is not open, returns { open: true }.
 */
export function focusFirstOptionOnKeyDown( event: KeyboardEvent, parentElement: HTMLDivElement | HTMLButtonElement, isDroplistOpen: boolean ): { open: boolean } {

  if ( event.key === 'ArrowDown' ) { // ------------------------------------- If the ArrowDown key is pressed

    event.preventDefault() // ----------------------------------------------- Prevent default behavior

    const focus = document.activeElement === parentElement // --------------- Check if the active element is the parent element

    if ( !focus )
      return { open: false } // --------------------------------------------- If the active element is not the parent element, return { open: false }

    if ( isDroplistOpen ) {

      const firstOption = document.getElementsByName( 'option-0' )[0] // ---- Get the first option element.

      if ( firstOption )
        firstOption.focus() // ---------------------------------------------- Focus the first option element if exists.

      return { open: true }

    }

    return { open: true }

  }

  if ( event.key === 'Tab' && !isDroplistOpen ) // -------------------------- If the Tab key is pressed and the droplist is not open, return { open: false }.
    return { open: false }

  if ( event.key === 'Tab' && isDroplistOpen ) { // ------------------------- If the Tab key is pressed and the droplist is open,

    event.preventDefault() // ----------------------------------------------- Prevent default behavior

    const firstOption
      = document.getElementsByName( 'search-option-sm' )[0] // -------------- Get the mobile search option element if it exists,
        ?? document.getElementsByName( 'search-option' )[0] // -------------- Otherwise, get the desktop search option element if it exists,
        ?? document.getElementsByName( 'option-0' )[0] // ------------------- Otherwise, get the first option element.

    if ( firstOption )
      firstOption.focus() // ------------------------------------------------ Focus the first option element if exists.

    return { open: true }

  }

  return { open: false }

}
