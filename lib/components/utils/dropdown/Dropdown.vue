<script setup lang="ts" generic="M extends boolean">

import { setupDropdown } from '@lib/components/utils/dropdown/utils'
import { onClickOutside } from '@vueuse/core'
import { searchModel, useBreakpoint } from '@lib/scripts/utils'
import { computed, onBeforeUnmount, ref, useTemplateRef, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import DropdownItem from '@lib/components/utils/dropdown/dropdown-item/DropdownItem.vue'

import type { CloseType } from '@lib/components/utils/dropdown/types'
import type { ReturnType, SelectModelType } from '@lib/components/inputs/input/select-input/types'

interface Props {
  input?:        SelectModelType<M>
  options?:      DropListOption[]
  multiple?:     M
  returnType?:   ReturnType
  enableSearch?: boolean
  parentElement: HTMLDivElement | HTMLButtonElement
}

defineOptions({ name: 'Dropdown' })

const props = withDefaults( defineProps<Props>(), {
  options:      () => ( [] ),
  teleport:     true,
  returnType:   'id',
  enableSearch: true,
})

const emits = defineEmits<{
  close:    [ type: CloseType ]
  pending:  [ pending: boolean ]
  selected: [ option: DropListOption ]
}>()

let destroy                 = null // -------------------------------------------------------------------------------------------------------- Destroy function for the dropdown.
const dirty                 = ref<boolean>( false ) // --------------------------------------------------------------------------------------- Indicates if the dropdown has been interacted with.
const query                 = ref<string>( '' ) // ------------------------------------------------------------------------------------------- Search query for the dropdown.
const pending               = ref<boolean>( false ) // --------------------------------------------------------------------------------------- Indicates if the action from the option is pending.
const mounted               = ref<boolean>( false ) // --------------------------------------------------------------------------------------- Trigger to show the dropdown.
const optionsCount          = computed(() => searchFilteredOptions.value.length ) // --------------------------------------------------------- Count of the filtered options.
const wrapperElement        = useTemplateRef( 'wrapperElement' ) // -------------------------------------------------------------------------- Reference to the wrapper element.
const dropdownElement       = useTemplateRef( 'dropdownElement' ) // ------------------------------------------------------------------------- Reference to the dropdown element.
const searchInputElement    = useTemplateRef( 'searchInputElement' ) // ---------------------------------------------------------------------- Reference to the search input element.
const sortedSelectedOptions = ref( [] ) // --------------------------------------------------------------------------------------------------- Sorted options, selected first, then the rest of the options. ( for multi select ).
const searchFilteredOptions = computed(() => searchModel( sortedSelectedOptions.value ?? [], query.value, [ 'name', 'description' ] )) // ---- Filter the options based on the search query.

onClickOutside( dropdownElement, () => closeList( 'outside' ), { ignore: [ '.ignore-outside-dropdown' ] })

/**
 * Set the pending state for the dropdown.
 * Emits the pending event when the state changes.
 * @param isPending
 */
function setPending( isPending: boolean ) {
  pending.value = isPending // -------- Set the pending state.
  emits( 'pending', isPending ) // ---- Emit the pending event.
}

/**
 * Sort the selected options for multi select based on the order of the options.
 * @param {DropListOption[]} options The options to sort.
 * @returns {DropListOption[]} The sorted options.
 */
function sortSelectedOptions( options: DropListOption[] ): DropListOption[] {

  if ( !props.multiple )
    return options // ------------------------- If multiple selection is not enabled, return the options as is.

  const sOptions: DropListOption[] = [] // ---- Empty array to store the selected options.
  const rOptions: DropListOption[] = [] // ---- Empty array to store the remaining options.

  for ( const option of options ) { // -------- For each option,

    if ( isOptionSelected( option )) // ------- If the option is selected,
      sOptions.push( option ) // -------------- Add it to the selected options.

    else rOptions.push( option ) // ----------- Else, add it to the remaining options.

  }

  return [ ...sOptions, ...rOptions ] // ------ Return the sorted options.

}

/**
 * Watch for changes in the options and sort the selected options.
 * Sort if multiple selection is enabled, otherwise return the options as is.
 * The sorting will be done every time when the dropdown is mounted or when the options change.
 */
watch(() => props.options, ( n ) => {
  sortedSelectedOptions.value = sortSelectedOptions( n.filter( o => !o.hidden ))
}, { immediate: true })

/**
 * Destroy the dropdown list.
 * This is used to clean up the dropdown when it is closed.
 */
function destroyList() {

  if ( !destroy )
    return

  destroy()

}

/**
 * Check if the option is selected.
 * @param option The option to check
 * @returns True if the option is selected, false otherwise
 */
function isOptionSelected( option: DropListOption ) {

  if ( props.multiple ) { // ----------------------------------- If multiple selection is enabled,

    return Array.isArray( props.input ) // --------------------- and if the input is an array,
      ? props.input.includes( option[props.returnType] ) // ---- Check if the input includes the option.
      : false // ----------------------------------------------- Else, return false.

  }

  return props.input === option[props.returnType] // ----------- Else, return true if the input is equal to the option.

}

/**
 * Close the dropdown list.
 * @param {CloseType} type The type of the close event.
 */
function closeList( type: CloseType ) {

  if ( pending.value ) { // ------------------------------ If the action is pending, return.
    return
  }

  mounted.value = false // ------------------------------- Set the mounted trigger to false.

  if ( !useBreakpoint( 'md' )) // ------------------------ If on small screen,
    setTimeout(() => emits( 'close', type ), 100 ) // ---- wait for 200ms before emitting the close event.

  else emits( 'close', type ) // ------------------------- Else, emit the close event immediately.

}

/**
 * Timeout for scrolling to the selected option.
 * This allows the dropdown to mount before scrolling
 * so we can be sure the selected option is visible.
 */
const optionScrollTimeout = ref<NodeJS.Timeout>( null )

/**
 * Scroll to the selected option if single select,
 * or scroll to the first selected option if multiple select.
 */
function scrollToSelectedOption() {

  clearTimeout( optionScrollTimeout.value )

  optionScrollTimeout.value = setTimeout(() => { // ------------------------------------------------------------------- Set a timeout to  allow the dropdown to mount.

    const selectedOption = dropdownElement.value?.querySelector( '.option-is-selected' ) as HTMLButtonElement // ------ Get the selected option.

    if ( !selectedOption || props.multiple ) { // --------------------------------------------------------------------- Return if there is no selected option or if multiple select is enabled.
      dropdownElement.value.scrollTop = 0
      return
    }

    selectedOption.scrollIntoView({ // -------------------------------------------------------------------------------- Scroll to the selected option.
      behavior: 'smooth',
    })

  }, 150 )

}

/**
 * Close the dropdown list when the escape key is pressed.
 * @param {KeyboardEvent} event The keyboard event.
 */
function closeListOnEscape( event: KeyboardEvent ) {

  if ( event.key === 'Escape' )
    closeList( 'escape' )

}

/**
 * Handle the Tab key press event.
 * @param e KeyboardEvent
 * This function manages the focus cycling through dropdown options when the Tab key is pressed.
 * It focuses the first or last option based on the current focus state.
 */
function handleTab( e: KeyboardEvent ) {

  if ( e.key !== 'Tab' ) {
    return
  }

  const currTarget      = e.target as HTMLButtonElement // ----------------------------------------------------------------- Get the current target element.
  const lastOption      = document.getElementsByName( `option-${optionsCount.value - 1}` )[0] as HTMLButtonElement // ------ Get the last option element.
  const firstOption     = document.getElementsByName( 'option-0' )[0] as HTMLButtonElement // ------------------------------ Get the first option element.
  const selectedOpt     = document.querySelectorAll( '.option-is-selected' )[0] as HTMLButtonElement // -------------------- Get the currently selected option.
  const searchOption    = document.getElementsByName( 'option-search' )[0] as HTMLInputElement // -------------------------- Get the search option element.
  const searchOptionSm  = document.getElementsByName( 'option-search-sm' )[0] as HTMLInputElement // ----------------------- Get the search option element.

  if ( currTarget.name === 'option-search' || currTarget.name === 'option-search-sm' ) { // -------------------------------- If the current target is the search option,

    e.preventDefault() // -------------------------------------------------------------------------------------------------- Prevent the default tab behavior.

    if ( !dirty.value && selectedOpt ) { // -------------------------------------------------------------------------------- If there is a selected option and this is first interaction,
      selectedOpt.scrollIntoView() // -------------------------------------------------------------------------------------- Scroll the selected option into view. ( This prevents the focus jump ).
      selectedOpt.focus() // ----------------------------------------------------------------------------------------------- Focus the selected option.
      dirty.value = true // ------------------------------------------------------------------------------------------------ Set dirty to true.
      return
    }

    firstOption.focus() // ------------------------------------------------------------------------------------------------- Focus the first option.
    return

  }

  if ( currTarget.name === lastOption?.name ) { // ------------------------------------------------------------------------- If the current target is the last option,

    e.preventDefault() // -------------------------------------------------------------------------------------------------- Prevent the default tab behavior,

    const option = searchOptionSm ?? searchOption ?? firstOption // -------------------------------------------------------- Get the search option if it exists, otherwise get the first option.
    option.focus() // ------------------------------------------------------------------------------------------------------ Focus the option.

  }

  if ( currTarget.name === 'options-parent' ) {

    e.preventDefault() // -------------------------------------------------------------------------------------------------- Prevent the default tab behavior.
    firstOption.focus() // ------------------------------------------------------------------------------------------------- Focus the first option.

  }

}

/**
 * Handle the ArrowDown key press event.
 * @param e KeyboardEvent
 * This function manages the focus cycling through dropdown options when the ArrowDown key is pressed.
 * It focuses the next option or the first option if at the end of the list.
 */
function handleArrowDown( e: KeyboardEvent ) {

  if ( e.key !== 'ArrowDown' )
    return

  e.preventDefault()

  const currTarget = e.target as HTMLButtonElement // ----------------------------------------------------- Get the current target element.
  const firstOption = document.getElementsByName( 'option-0' )[0] // -------------------------------------- Get the first option element.
  const nextSibling = currTarget.nextSibling as HTMLButtonElement // -------------------------------------- Get the next sibling element of the current target.
  const selectedOpt = document.querySelectorAll( '.option-is-selected' )[0] as HTMLButtonElement // ------- Get the currently selected option.

  if ( currTarget.name === 'option-search' || currTarget.name === 'option-search-sm' ) { // --------------- If the current target is the search option,

    if ( !dirty.value && !!selectedOpt ) { // ------------------------------------------------------------- If there is a selected option and this is first interaction,
      selectedOpt.scrollIntoView() // --------------------------------------------------------------------- Scroll the selected option into view. ( This prevents the focus jump ).
      selectedOpt.focus() // ------------------------------------------------------------------------------ Focus the selected option.
      dirty.value = true // ------------------------------------------------------------------------------- Set dirty to true.
      return
    }

    firstOption.focus() // -------------------------------------------------------------------------------- Focus the first option.
    return

  }

  if ( nextSibling?.name?.match( 'option' )) { // --------------------------------------------------------- If there is a next option,
    nextSibling.focus() // -------------------------------------------------------------------------------- Focus the next option.
    return
  }

  if ( !nextSibling?.name?.match( 'option' )) { // --------------------------------------------------------- If there is no next option,

    const searchOption    = document.getElementsByName( 'option-search' )[0] as HTMLInputElement // ------- Get the search option element.
    const searchOptionSm  = document.getElementsByName( 'option-search-sm' )[0] as HTMLInputElement // ---- Get the search option element.

    const option = searchOptionSm ?? searchOption ?? selectedOpt ?? firstOption // ------------------------ If there is no search option, get the first option.
    option.focus() // ------------------------------------------------------------------------------------- Focus the option.

    return

  }

  firstOption.focus() // ---------------------------------------------------------------------------------- Focus the first option if no case matches.

}

/**
 * Handle the ArrowUp key press event.
 * @param e KeyboardEvent
 * This function manages the focus cycling through dropdown options when the ArrowUp key is pressed.
 * It focuses the previous option or the last option if at the beginning of the list.
 */
function handleArrowUp( e: KeyboardEvent ) {

  if ( e.key !== 'ArrowUp' )
    return

  e.preventDefault()

  const currTarget      = e.target as HTMLButtonElement // ---------------------------------------------- Get the current target element.
  const lastOption      = document.getElementsByName( `option-${optionsCount.value - 1}` )[0] // -------- Get the last option element.
  const prevSibling     = currTarget.previousSibling as HTMLButtonElement // ---------------------------- Get the previous sibling element of the current target.
  const searchOption    = document.getElementsByName( 'option-search' )[0] // --------------------------- Get the search option element.
  const searchOptionSm  = document.getElementsByName( 'option-search-sm' )[0] // ------------------------ Get the search option element.

  if ( currTarget.name === 'option-0' ) { // ------------------------------------------------------------ If the current target is the first option,
    const option = searchOptionSm ?? searchOption ?? lastOption // -------------------------------------- Get the search option if it exists, otherwise get the last option.
    option.focus() // ----------------------------------------------------------------------------------- Focus the option.
    return
  }

  if ( currTarget.name === 'option-search' || currTarget.name === 'option-search-sm' ) { // ------------- If the current target is the search option,
    lastOption.focus() // ------------------------------------------------------------------------------- Focus the last option.
    return
  }

  if ( prevSibling?.name?.match( 'option' )) { // ------------------------------------------------------- If there is a previous sibling that matches 'option',
    prevSibling.focus() // ------------------------------------------------------------------------------ Focus the previous sibling.
  }

}

/**
 * Cycle focus through dropdown options based on keyboard events.
 * @param e KeyboardEvent
 * This function listens for keydown events and manages focus cycling through dropdown options.
 */
function cycleFocus( e: KeyboardEvent ) {

  handleTab( e )
  handleArrowUp( e )
  handleArrowDown( e )

}

/**
 * Watch for the wrapper element to mount and set the mounted trigger.
 * This is used for the transition of the dropdown list.
 */
watch( wrapperElement, n => mounted.value = !!n, { immediate: true })

/**
 * Watch for the dropdown element to mount and set up the dropdown.
 */
watch( dropdownElement, ( n ) => {

  if ( n ) {

    destroy = setupDropdown( n, props.parentElement )

    scrollToSelectedOption()

    window.addEventListener( 'keydown', cycleFocus )
    window.addEventListener( 'keydown', closeListOnEscape )
    props.parentElement.classList.add( 'ignore-outside-dropdown' )

    searchInputElement.value?.focus()

  }

}, { immediate: true })

onBeforeUnmount(() => {

  destroyList()

  window.removeEventListener( 'keydown', cycleFocus )
  window.removeEventListener( 'keydown', closeListOnEscape )
  props.parentElement.classList.remove( 'ignore-outside-dropdown' )

})

</script>

<template>

  <Teleport to="body">

    <div
      ref="wrapperElement"
      class="dropdown-wrapper ignore-outside"
      :class="{ 'dropdown-wrapper-mounted': mounted }"
    >

      <Transition name="open-dropdown">

        <div
          v-if="mounted"
          ref="dropdownElement"
          class="dropdown"
          data-dropdown="true"
        >

          <slot name="header" />

          <slot>

            <div v-if="!useBreakpoint('md') && enableSearch" class="dropdown-search-bar">

              <div class="dropdown-search-bar-input-block">

                <Icon
                  name="search"
                  class="dropdown-search-bar-icon"
                />

                <input
                  ref="searchInputElement"
                  v-model="query"
                  class="dropdown-search-bar-input"
                  :name="enableSearch ? 'option-search-sm' : undefined"
                  :disabled="pending"
                  placeholder="Search Options"
                >

              </div>

              <Button
                size="l"
                type="box"
                mode="naked"
                icon="close"
                :disabled="pending"
                @click="closeList('button')"
              />

            </div>

            <DropdownItem
              v-for="option, index in searchFilteredOptions.filter(o => !o.hidden)"
              :key="option.id ?? `${option.name} ${index}`"
              :index="index"
              :total="options.length"
              :option="option"
              :multiple="multiple"
              :selected="isOptionSelected(option)"
              :disabled="pending || option.disabled"
              @close="closeList('select')"
              @select="(o) => emits('selected', o)"
              @pending="setPending"
            />

          </slot>

        </div>

      </Transition>

    </div>

  </Teleport>

</template>
