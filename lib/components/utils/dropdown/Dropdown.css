@import '@lib/components/utils/dropdown/dropdown-item/DropdownItem.css';

.default-design-system {

  .dropdown-wrapper {

    @apply w-full md:w-auto h-full md:h-auto absolute z-30 md:static bottom-0 bg-overlay md:bg-transparent md:backdrop-blur-none opacity-0 overflow-hidden transition-all;

    &.dropdown-wrapper-mounted {
      @apply backdrop:blur-xs opacity-100;
    }

    .dropdown {

      @apply w-full md:w-max md:min-w-max absolute md:fixed z-10 max-h-full md:max-h-none grid overflow-hidden overflow-y-auto overscroll-contain bg-layer-01 shadow-2xl;

      .dropdown-search-bar {

        @apply h-12 sticky top-0 z-1 md:hidden grid grid-cols-[1fr_max-content] place-content-center bg-layer-02 border-b border-border-subtle-00;

        .dropdown-search-bar-input-block {

          @apply 
          h-full pl-4 flex items-center
          focus-within:outline-focus focus-within:outline-2 focus-within:-outline-offset-2;

          .dropdown-search-bar-icon {
            @apply text-icon-secondary;
          }

          .dropdown-search-bar-input {
            @apply text-sm w-full h-full px-4 placeholder:text-text-placeholder outline-0;
          }

        }

      }

    }


  }

}