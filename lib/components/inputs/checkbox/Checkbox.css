.default-design-system {

  .checkbox-wrapper {

    @apply h-5 flex items-center;

    .checkbox-label {
      
      @apply text-sm h-full w-max flex items-center gap-x-2;

      .checkbox {

        &.radio { @apply rounded-full; }

        @apply relative size-5 flex items-center justify-center focus-within:outline-2 focus-within:-outline-offset-1 focus-within:outline-focus;

        .checkbox-input {
          @apply appearance-none m-0 bg-transparent;
        }

      }

    }

    &.checkbox-is-reversed {

      .checkbox-label {
        @apply flex-row-reverse;
      }

    }

    &.checkbox-is-disabled {

      .checkbox-label {
        
        .checkbox {

          .checkbox-icon {
            @apply text-icon-disabled;
          }

        }

        .checkbox-label-text {
          @apply text-text-disabled;
        }

      }

    }

    &.checkbox-is-readonly {

      .checkbox-label { @apply pointer-events-none; }

    }

  }

}