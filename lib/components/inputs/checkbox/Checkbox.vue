<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

interface Props {
  label?:    string
  radio?:    boolean
  partial?:  boolean
  reverse?:  boolean
  disabled?: boolean
  readonly?: boolean
}

defineOptions({ name: 'Checkbox' })

defineProps<Props>()

const input = defineModel<boolean>()

</script>

<template>

  <div
    class="checkbox-wrapper"
    :class="{
      'checkbox-is-disabled': disabled,
      'checkbox-is-readonly': readonly,
      'checkbox-is-reversed': reverse,
    }"
  >

    <slot name="prefix" />

    <label class="checkbox-label">

      <div class="checkbox" :class="{ radio }">

        <Icon
          v-if="input"
          size="m"
          class="checkbox-icon"
          :name="radio ? 'radio-checked' : 'checkbox-checked'"
        />
        <Icon
          v-else-if="partial"
          size="m"
          name="checkbox-partial"
          class="checkbox-icon"
        />

        <Icon
          v-else
          size="m"
          class="checkbox-icon"
          :name="radio ? 'radio-unchecked' : 'checkbox-unchecked'"
        />

        <input
          v-model="input"
          type="checkbox"
          class="checkbox-input"
          :disabled="disabled"
          :readonly="readonly"
          :tabindex="readonly ? -1 : 0"
          @keydown.enter="input = !input"
        >

      </div>

      <slot><span v-if="label" class="checkbox-label-text">{{ label }}</span></slot>

    </label>

    <slot name="suffix" />

  </div>

</template>
