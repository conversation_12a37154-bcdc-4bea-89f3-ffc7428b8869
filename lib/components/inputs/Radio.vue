<script setup lang="ts">

import { checkValue } from '@lib/scripts/utils'
import { computed, ref, watch } from 'vue'

import Checkbox from '@lib/components/inputs/checkbox/Checkbox.vue'
import OptionsGroup from '@lib/components/blocks/OptionsGroup.vue'
import ValidationWrapper from '@lib/components/utils/ValidationWrapper.vue'

import type { RadioInputProps } from '@lib/types/inputTypes'

interface RadioOption {
  id:        string | number
  name:      string
  hidden?:   boolean
  selected:  boolean
  disabled?: boolean
}

defineOptions({
  name: 'RadioInput'
})

const props = withDefaults( defineProps<RadioInputProps<string | number>>(), {
  direction:  'vertical',
  returnType: 'id'
})

const emits = defineEmits<{
  'update:valid':      [ payload: boolean ]
  'update:modelValue': [ payload: string | number ]
}>()

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

const selectableOptions = ref<RadioOption[]>( [] )

watch(() => props.options, ( n ) => {

  if ( checkValue( n )) {

    selectableOptions.value = n.map( option => ({ id: option.id, name: option.name, disabled: option.disabled, hidden: option.hidden, selected: false }))
    selectableOptions.value.filter( option => !option.hidden )

    const selectedOption = selectableOptions.value.find( option => option.id === props.modelValue )
    if ( selectedOption )
      handleRadio( selectedOption )

  }

}, { immediate: true })

watch( input, ( n ) => {

  if ( checkValue( n )) {

    selectableOptions.value.forEach(( option ) => {

      option.selected = false

      if ( option.id === n ) {
        option.selected = true
        emits( 'update:valid', true )
      }

    })

  }

  // When value is updated from outside to null, remove selection, set valid to false
  if ( n === null && props.nullable ) {

    emits( 'update:valid', false )

    selectableOptions.value.forEach(( option ) => {

      option.selected = false

    })

  }

}, { immediate: true })

function handleRadio( option: RadioOption ) {

  if ( option.selected ) {

    if ( !props.nullable )
      return

    option.selected = false
    input.value = null

    emits( 'update:valid', false )

  }

  else {
    selectableOptions.value.forEach(( o ) => {
      o.selected = false
      if ( o.id === option.id ) {
        o.selected = true
        input.value = o[props.returnType]
      }
    })
    emits( 'update:valid', true )
  }

}

</script>

<template>

  <ValidationWrapper v-bind="props">

    <OptionsGroup
      :label="label"
      :required="required"
      :direction="direction"
    >

      <Checkbox
        v-for="option, index in selectableOptions"
        :key="option.id"
        :name="`radio-${index}`"
        :radio="true"
        :model-value="option.selected"
        :disabled="option.disabled || disabled"
        :transparent="true"
        :class="{
          'pointer-events-none': !nullable && option.selected,
        }"
        @update:model-value="() => handleRadio(option)"
      >
        {{ option.name }}
      </Checkbox>

    </OptionsGroup>

  </ValidationWrapper>

</template>
