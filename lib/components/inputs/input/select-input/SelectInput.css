.input {

  /* ----------------------------------- Select Input [ SINGLE-SELECT ] */

  &.input-type-select {

    .label-main-block {

      .label-nested-block {

        @apply focus-within:after:border-2;

        .label-input-slot {

          .select-input-block {

            @apply w-full h-full relative flex items-center;

            .select-input {
              @apply
                w-full h-full px-4 bg-transparent outline-hidden transition-[padding-top]
                placeholder:text-transparent focus:placeholder:text-text-placeholder;
            }

            &:focus-within {

              .select-input {
                @apply pt-[0.75rem];
              }

            }

            &.select-input-open {

              .select-input {

                @apply pt-[0.75rem] placeholder:text-text-placeholder;

                &.select-input-search {
                  @apply placeholder:text-text-placeholder;
                }

              }

              .select-input-toolbar {

                .select-input-decorator {
                  @apply rotate-180;
                }

              }

            }

            .select-input-decorator {
              @apply transition-transform;
            }

          }

          &.m {
            
            .select-input-block {
              
              &:focus-within {

                .select-input {
                  @apply pt-[0.6rem];
                }

              }

            }
              

          }

          &.s {
            
            .select-input-block {
              
              &:focus-within {

                .select-input {
                  @apply pt-0;
                }

              }

            }
              

          }

        }

        .label-input-slot:has(.select-input-open) {

          .label-text-dynamic {
            @apply top-[0.531rem] translate-y-0 text-[0.625rem] uppercase text-text-secondary;
          }

          .select-input-decorator {
            @apply rotate-180;
          }

          &.m {

            .label-text-dynamic {
              @apply top-[0.2rem];
            }

            .select-input-block {
              
              .select-input {
                @apply pt-[0.6rem];
              }
            }

          }

          &.s {

            .label-text-dynamic {
              @apply hidden;
            }

            .select-input-block {
              
              .select-input {
                @apply pt-0;
              }
            }

          }

        }

      }

      &.input-has-value {

        .label-nested-block {
          
          .label-input-slot {

            .select-input-block {

              .select-input {
                @apply pt-[0.75rem] placeholder:text-text-primary;
              }

            }

            &.m {

              .label-text-dynamic {
                @apply top-[0.2rem];
              }

              .select-input-block {
                
                .select-input {
                  @apply pt-[0.6rem];
                }
              }

            }

            &.s {

              .label-text-dynamic {
                @apply hidden;
              }

              .select-input-block {
                
                .select-input {
                  @apply pt-0;
                }
              }

            }

          }

        }

      }

      &.input-is-disabled {

        .label-nested-block {
          
          .label-input-slot {

            .select-input-block {

              @apply pointer-events-none;

              .input-toolbar {

                .select-input-decorator {
                  @apply text-text-disabled pointer-events-none;
                }

              }

            }

          }

        }

      }

    }

  }

  /* ----------------------------------- Select Input [ MULTI-SELECT ] */

  &.input-type-select:has(.multi-select) {

    .label-main-block {

      .label-text-static {
        @apply text-xs text-text-secondary inline-block pl-4 pb-2;
      }

      .label-nested-block {

        .label-input-slot {

          .label-text-dynamic {
            @apply hidden;
          }

          .select-input-block {

            .select-input {
              @apply w-full h-full px-4 bg-transparent outline-hidden placeholder:text-text-primary;
            }

            &:focus-within {

              .select-input {
                @apply pt-0;
              }

            }

            &.select-input-open {

              .select-input {
                @apply pt-0;
              }

            }

          }

        }

      }

      &.input-has-value {

        .label-nested-block {
          
          .label-input-slot {

            .select-input-block {

              .select-input-count-block {

                @apply h-full pl-4 grid place-content-center;

                .select-input-count-badge {
                  @apply text-xs bg-tag-background-gray-inverse h-6 px-3 grid place-content-center text-tag-color-gray-inverse rounded-full;
                }

              }

              .select-input {
                @apply pt-0 pl-2;
              }

            }

          }

        }

      }

      &.input-is-disabled {

        .label-nested-block {
          
          .label-input-slot {

            .select-input-block {

              @apply pointer-events-none;

              .select-input-count-block {

                @apply h-full pl-4 grid place-content-center;

                .select-input-count-badge {
                  @apply opacity-50;
                }

              }

              .select-input {
                @apply pointer-events-none text-text-disabled placeholder:text-text-disabled;
              }

              .input-toolbar {

                .select-input-decorator {
                  @apply text-text-disabled pointer-events-none;
                }

              }

            }

          }

        }

      }

    }

  }

}
