<script setup lang="ts" generic="M extends boolean = false">

import { useI18n } from 'vue-i18n'
import { checkValue, searchModel } from '@lib/scripts/utils'
import { focusFirstOptionOnKeyDown } from '@lib/components/utils/dropdown/utils'
import { computed, ref, useTemplateRef, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import Dropdown from '@lib/components/utils/dropdown/Dropdown.vue'
import InputStatusBadge from '@lib/components/inputs/input/InputStatusBadge.vue'

import type { CloseType } from '@lib/components/utils/dropdown/types'
import type { SelectInputProps, SelectModelType } from '@lib/components/inputs/input/select-input/types'

defineOptions({ name: 'SelectInput' })

const props = withDefaults( defineProps<SelectInputProps<'select', M>>(), {
  options:      () => [],
  nullable:     true,
  required:     true,
  returnType:   'id',
  enableSearch: true
})

const { t }           = useI18n() // ------------------------------------------------------------------------------------- i18n instance
const input           = defineModel<SelectModelType<M>>() // ------------------------------------------------------------- Binded input value
const focus           = ref<boolean>( false ) // ------------------------------------------------------------------------- Indicates if the input is focused
const pending         = ref<boolean>( false ) // ------------------------------------------------------------------------- Indicates if the action from the option is pending.
const toggleList      = ref<boolean>( false ) // ------------------------------------------------------------------------- Toggles list visibility
const searchQuery     = ref<string>( '' ) // ----------------------------------------------------------------------------- Search query for the dropdown list
const booleanModel    = defineModel<boolean>( 'booleanModel', { default: null }) // -------------------------------------- Binded boolean value
const selectElement   = useTemplateRef<HTMLDivElement>( 'selectElement' ) // --------------------------------------------- Select element reference
const selectInputEl   = useTemplateRef<HTMLInputElement>( 'selectInputEl' ) // ------------------------------------------- Select input element reference
const canRemoveInput  = computed(() => props.nullable && checkValue( input.value ) && !searchQuery.value ) // ------------ Check if input value is not null and nullable prop is true.
const filteredOptions = computed(() => searchModel( props.options, searchQuery.value, [ 'name', 'description' ] )) // ---- Filtered options based on search query

/**
 * Watches for changes in the boolean model and input value.
 * If the input value is a boolean or null/undefined, it updates the input value based on the boolean model.
 */
watch( [ booleanModel, input ], ( n ) => {

  const [ b, i ] = n // ----------------------------------------------- Destructure the new values

  if (
    ( typeof i === 'boolean' || i === null || i === undefined ) // ---- If the input value is a boolean or null/undefined
    && typeof b === 'boolean' // -------------------------------------- And the boolean model value is a boolean
  ) {

    const option = props.options.find(( item ) => { // ---------------- Find the option that matches the boolean model value

      let booleanValue: boolean = null

      if ( [ 'true', 'false' ].includes( item?.mapToBoolean )) // ----- If the item has a mapToBoolean property
        booleanValue = item.mapToBoolean === 'true' // ---------------- Convert it to a boolean value

      return booleanValue === b // ------------------------------------ Check if the boolean value matches the boolean model value

    })

    input.value = option ? option[props.returnType] : null // --------- Set the input value to the found option or null.

  }

}, { immediate: true })

/**
 * Selects an option from the dropdown list.
 * @param option The selected option
 */
function selectOption( option: DropListOption ) {

  // Handle multiple options select

  // If the multiple prop is true, the input value will be an array of selected options.
  // If the nullable prop is false, the input array must have at least one selected option.

  if ( props.multiple ) {

    const isInputArray = Array.isArray( input.value )

    if ( isInputArray ) { // -------------------------------------------- If input is an array

      let list = [ ...input.value as SelectModelType<true> ] // --------- Get current input value as array

      if ( list.includes( option[props.returnType] )) { // -------------- If option is already selected

        if ( !props.nullable ) { // ------------------------------------- If not nullable

          if ( list.length === 1 ) // ----------------------------------- Return if there is only one selected option.
            return

        }

        list = list.filter( o => o !== option[props.returnType] ) // ---- Remove selected option if already selected and nullable

      }

      else { list.push( option[props.returnType] ) } // ----------------- Add selected option to the list if it's not already selected

      input.value = list as SelectModelType<M> // ----------------------- Set input value to the new list

    }

    else { // ----------------------------------------------------------- If input is not an array

      const list: typeof props.returnType[] = [] // --------------------- Create a new list
      list.push( option[props.returnType] ) // -------------------------- Add selected option to the list

      input.value = list as SelectModelType<M> // ----------------------- Set input value to the new list

    }

  }

  // Handle single option select

  else { // ------------------------------------------------------------- If multiple prop is false

    if ( input.value === option[props.returnType] ) { // ---------------- If option is already selected

      if ( !props.nullable ) // ----------------------------------------- Return if not nullable.
        return

      input.value = null // --------------------------------------------- Set input value to null if nullable.
      booleanModel.value = null // -------------------------------------- Set boolean model value to null.

    }

    else { // ----------------------------------------------------------- If option is not selected

      input.value = option[props.returnType] // ------------------------- Set input value to the selected option
      booleanModel.value = option?.mapToBoolean === 'true' // ----------- Set boolean model value to true

    }

    selectInputEl.value.focus() // -------------------------------------- Focus the select input

  }

}

/**
 * Displays the selected option/s name.
 * @returns {string} The selected option name
 */
function displayInput(): string {

  // Handle multiple options display

  if ( props.multiple ) {

    if ( checkValue( input.value )) {

      if ( !props.enableSearch || !toggleList.value ) // ----------------------------------------------- If there is value but search is disabled or not focused,
        return t( 'global.label.option', Array.isArray( input.value ) ? input.value.length : 1 ) // ---- Display the number of selected options.

      return t( 'global.label.searchOptions' ) // ------------------------------------------------------ Else display the search placeholder.

    }

    if ( !props.enableSearch || !toggleList.value ) // ------------------------------------------------- If there is no value and search is disabled or not focused,
      return props.placeholder ?? t( 'global.label.chooseOption', 2 ) // ------------------------------- Display the placeholder prop or default placeholder.

    return t( 'global.label.searchOptions' ) // -------------------------------------------------------- Else display the search placeholder.

  }

  // Handle single option display

  if ( checkValue( input.value )) {

    if ( !props.enableSearch || !toggleList.value ) // ------------------------------------------------- If there is value but search is disabled or not focused,
      return props.options.find( option => option[props.returnType] === input.value )?.name // --------- Display the selected option name.

    return t( 'global.label.searchOptions' ) // -------------------------------------------------------- Else display the search placeholder.

  }

  if ( !props.enableSearch || !toggleList.value ) // --------------------------------------------------- If there is no value and search is disabled or not focused,
    return props.placeholder ?? t( 'global.label.chooseOption', 1 ) // --------------------------------- Display the placeholder prop or default placeholder.

  return t( 'global.label.searchOptions' ) // ---------------------------------------------------------- Else display the search placeholder.

}

/**
 * Removes the selected input value.
 */
function removeInput() {

  input.value = null
  booleanModel.value = null
  selectInputEl.value.focus()

}

/**
 * Removes the search query and focuses the select input.
 */
function removeSearchQuery() {

  searchQuery.value = '' // --------- Reset search query when removing it
  selectInputEl.value.focus() // ---- Focus the select input after removing search query

}

/**
 * Closes the dropdown list and resets the search query.
 */
function closeList( type: CloseType ) {

  if ( pending.value ) // ------------- If the action is pending, return.
    return

  if ( type !== 'outside' ) {
    selectInputEl.value.focus() // ---- Focus the select input when closing the list
  }

  toggleList.value = false // --------- Close the dropdown list
  searchQuery.value = null // --------- Reset search query when closing the list

}

/**
 * Opens the dropdown list and focuses the select input.
 * If search is enabled, it resets the search query.
 */
function openList() {

  if ( !props.enableSearch ) {
    toggleList.value = true
    return
  }

  if ( toggleList.value ) {
    selectInputEl.value.focus()
    return
  }

  toggleList.value = true
  searchQuery.value = null
  selectInputEl.value.focus()

}

/**
 * Focuses the first option in the dropdown list when the user presses the tab, space, or arrow down key.
 * If the list is not open, it opens the list.
 * @param e The keyboard event
 */
function focusFirstOption( e: KeyboardEvent ) {

  focusFirstOptionOnKeyDown( e, selectElement.value, toggleList.value )

  openList()

}

/**
 * Prevents the first input space from being added to the search query.
 * If the search query is empty, it prevents the default action of the space key.
 * @param e The keyboard event
 */
function preventFirstInputSpace( e: KeyboardEvent ) {

  focusFirstOption( e )

  if ( !searchQuery.value || searchQuery.value.length === 0 )
    e.preventDefault()

}

</script>

<template>

  <div
    ref="selectElement"
    class="select-input-block"
    :name="name"
    :class="{ 'select-input-open': toggleList, 'multi-select': multiple }"
    @click="() => openList()"
  >

    <div
      v-if="Array.isArray(input) && checkValue(input)"
      class="select-input-count-block"
    >

      <div class="select-input-count-badge">
        {{ input.length }}
      </div>

    </div>

    <input
      ref="selectInputEl"
      v-model="searchQuery"

      class="select-input"
      :name="enableSearch && toggleList ? 'option-search' : 'options-parent'"
      :class="{ 'select-input-search': enableSearch }"
      :readonly="!toggleList || !enableSearch"
      :disabled="disabled || pending"
      :placeholder="displayInput()"

      @blur="focus = false"
      @focus="focus = true"
      @keydown.space="preventFirstInputSpace"
      @keydown.arrow-down="focusFirstOption"
    >

    <div class="input-toolbar">

      <InputStatusBadge :status="status" />

      <div v-if="canRemoveInput" class="input-decorator ignore-outside-dropdown">

        <Button
          size="xs"
          type="box"
          mode="naked"
          icon="close-thin"
          icon-size="s"
          :disabled="disabled || pending"
          @click.stop.prevent="removeInput"
        />

      </div>

      <div v-if="!!searchQuery" class="input-decorator ignore-outside-dropdown">

        <Button
          size="xs"
          type="box"
          mode="naked"
          icon="close-thin"
          icon-size="s"
          :disabled="disabled || pending"
          @click.stop.prevent="removeSearchQuery"
        />

      </div>

      <div
        class="input-decorator select-input-decorator ignore-outside-dropdown"
        @click.stop.prevent="toggleList ? closeList('button') : openList()"
      >
        <Icon name="chevron-down-thin" />
      </div>

    </div>

    <Dropdown
      v-if="toggleList"
      :input="input"
      :options="filteredOptions"
      :multiple="multiple"
      :return-type="returnType"
      :enable-search="enableSearch"
      :parent-element="selectElement"
      @close="closeList"
      @selected="selectOption"
      @pending="(p) => pending = p"
    />

  </div>

</template>
