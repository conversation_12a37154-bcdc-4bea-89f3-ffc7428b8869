import type { InputType, SharedInputProps } from '@lib/components/inputs/input/types'

export type ReturnType = 'id' | 'name'
export type SelectModelType<Multiple extends boolean = false> = Multiple extends true ? ( string | number )[] : string | number

export interface SelectInputProps<T extends InputType, Multiple extends boolean> extends SharedInputProps {
  options?:      T extends 'select' ? DropListOption[] : never
  multiple?:     T extends 'select' ? Multiple : never
  nullable?:     T extends 'select' ? boolean : never
  returnType?:   T extends 'select' ? ReturnType : never
  enableSearch?: T extends 'select' ? boolean : never
  booleanModel?: T extends 'select' ? boolean : never
}

export interface SelectInputValidationOptions<M extends boolean> {
  minItems?: M extends true ? number : never
  maxItems?: M extends true ? number : never
}
