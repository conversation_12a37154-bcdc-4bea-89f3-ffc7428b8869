<script setup lang="ts" generic="T extends InputType, Multiple extends boolean = false">

import { checkValue } from '@lib/scripts/utils'
import { computed, ref, watch } from 'vue'
import { trimInput, validateInput } from '@lib/components/inputs/input/utils'

import TextInput from '@lib/components/inputs/input/text-input/TextInput.vue'
import EmailInput from '@lib/components/inputs/input/email-input/EmailInput.vue'
import SelectInput from '@lib/components/inputs/input/select-input/SelectInput.vue'
import NumberInput from '@lib/components/inputs/input/number-input/NumberInput.vue'
import PasswordInput from '@lib/components/inputs/input/password-input/PasswordInput.vue'
import CurrencyInput from '@lib/components/inputs/input/currency-input/CurrencyInput.vue'
import TextareaInput from '@lib/components/inputs/input/textarea-input/TextareaInput.vue'

import type { Component } from 'vue'
import type { InputModel, InputProps, InputType } from '@lib/components/inputs/input/types'

defineOptions({ name: 'InputField' })

const props = withDefaults( defineProps<InputProps<T, Multiple>>(), { size: 'l', required: true })
const emits = defineEmits<{ 'update:modelValue': [InputModel<T, Multiple>] }>()

const inputs: Record<InputType, Component> = {
  text:     TextInput,
  email:    EmailInput,
  number:   NumberInput,
  select:   SelectInput,
  password: PasswordInput,
  currency: CurrencyInput,
  textarea: TextareaInput
}

const input = computed({
  get: () => {

    const cleanValue = typeof props.modelValue === 'string' // ---------------- if the model value is a string,
      ? trimInput( props.modelValue ) // -------------------------------------- Trim the input value,
      : props.modelValue // --------------------------------------------------- Otherwise, return the model value as is.

    return cleanValue as InputModel<T, Multiple> // --------------------------- Return the cleaned value as InputModel.

  },
  set: ( value ) => {

    const cleanValue = typeof value === 'string' // --------------------------- if the value is a string,
      ? trimInput( value ) // ------------------------------------------------- Trim the input value,
      : value // -------------------------------------------------------------- Otherwise, return the value as is.

    emits( 'update:modelValue', cleanValue as InputModel<T, Multiple> ) // ---- Emit the update event with the cleaned value.

  }
})

const valid         = defineModel<boolean>( 'valid' ) // ----------------------------------------------------------------- Indicates if the input is valid.
const error         = ref<string>( null ) // ----------------------------------------------------------------------------- Error message, Indicates if the input has an error.
const warning       = ref<string>( null ) // ----------------------------------------------------------------------------- Warning message, Indicates if the input has a warning.
const isDirty       = ref<boolean>( false ) // --------------------------------------------------------------------------- Indicates if the input has been modified.
const timeout       = ref<NodeJS.Timeout>( null ) // --------------------------------------------------------------------- Timeout for the validation.
const inputStatus   = computed(() => error.value ? 'error' : warning.value ? 'warning' : props.status ) // --------------- Input Status, can be 'error', 'warning' or null.
const booleanModel  = defineModel<boolean>( 'booleanModel' ) // ---------------------------------------------------------- Binded Boolean Model Value. ( Select Input Only )

/**
 * Input validation.
 * Watch the input value and the required prop.
 * If the input is not used (dirty), do not set the error.
 */
watch( [ input, () => props.required ], ( n ) => {

  error.value = null // --------------------------------------------------------- Reset the error message.
  clearTimeout( timeout.value ) // ---------------------------------------------- Clear the validation timeout.

  if ( checkValue( n[0] )) // --------------------------------------------------- If the input value is empty, do not validate.
    isDirty.value = true

  const inputName = props?.name // ---------------------------------------------- If there is no name prop,
    ?? typeof props?.label === 'string' // -------------------------------------- If the label prop is a string,
    ? String( props?.label ) // ------------------------------------------------- Use the label prop as the name.
    : null // ------------------------------------------------------------------- Otherwise, use null. The validation will set a default name.

  const validation = validateInput(
    n[0], // -------------------------------------------------------------------- The new input value.
    n[1], // -------------------------------------------------------------------- The required prop.
    props.type,
    inputName,
    props.validationOptions
  )

  valid.value = validation.valid // --------------------------------------------- Set the valid value.

  if ( !isDirty.value ) // ------------------------------------------------------ If the input is not dirty, do not set the error.
    return

  timeout.value = setTimeout(() => error.value = validation.error, 1000 ) // ---- Debounce the error.

}, { immediate: true })

</script>

<template>

  <div :class="`input input-type-${type ?? 'text'}`" :data-input-type="type">

    <label
      class="label-main-block"
      :class="{
        'input-has-value': checkValue(input),
        'input-has-error': inputStatus === 'error',
        'input-has-warning': inputStatus === 'warning',
        'input-is-disabled': disabled,
      }"
    >

      <p v-if="label" class="label-text-static">{{ label }} <span v-if="required" class="label-required-decorator">*</span></p>

      <div class="label-nested-block">

        <slot name="prefix" />

        <slot>

          <div
            class="label-input-slot"
            :class="size"
          >

            <p class="label-text-dynamic">{{ label }} <span v-if="required" class="label-required-decorator">*</span></p>

            <Component
              :is="inputs[type] ?? TextInput"
              v-model="input"
              v-model:number-model="input"
              v-model:boolean-model="booleanModel"
              :size="size"
              :name="name ?? typeof label === 'string' ? String(label) : null"
              :step="step"
              :label="label"
              :strict="strict"
              :status="inputStatus"
              :options="options"
              :multiple="multiple"
              :required="required"
              :readonly="readonly"
              :disabled="disabled"
              :nullable="nullable"
              :assertive="assertive"
              :placeholder="placeholder"
              :show-controls="showControls"
              :enable-search="enableSearch"
              :validation-options="validationOptions"
            />

          </div>

        </slot>

        <slot name="suffix" />

      </div>

    </label>

    <p v-if="inputStatus === 'error'" class="input-text-error-message">
      <span v-html="error || message" />
    </p>

    <p v-else-if="inputStatus === 'warning'" class="input-text-warning-message">
      <span v-html="warning || message" />
    </p>

    <p v-else-if="message" class="input-text-helper-message">

      <slot name="message">
        <span v-html="message" />
      </slot>

    </p>

  </div>

</template>
