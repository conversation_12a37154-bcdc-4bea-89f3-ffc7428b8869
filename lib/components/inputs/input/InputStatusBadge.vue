<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

import type { InputStatus } from '@lib/components/inputs/input/types'

defineOptions({ name: 'InputStatusBadge' })

defineProps<{
  status?: InputStatus
}>()

</script>

<template>

  <div v-if="!!status" class="input-status">

    <Icon v-if="status === 'error'" name="status-icon-error-filled" />
    <Icon v-if="status === 'warning'" name="status-icon-warning-filled" />

  </div>

</template>
