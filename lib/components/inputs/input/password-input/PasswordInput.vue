<script setup lang="ts">

import { ref } from 'vue'

import Button from '@lib/components/button/Button.vue'
import InputStatusBadge from '@lib/components/inputs/input/InputStatusBadge.vue'

import type { SharedInputProps } from '@lib/components/inputs/input/types'

defineOptions({ name: 'PasswordInput' })

withDefaults( defineProps<SharedInputProps>(), { placeholder: '••••••••' })

const input = defineModel<string>()
const togglePassword = ref( false )

</script>

<template>

  <div class="input-block">

    <input
      v-model="input"
      :name="name"
      class="password-input"
      :type="togglePassword ? 'text' : 'password'"
      :disabled="disabled"
      :placeholder="placeholder"
      autocomplete="new-password"
    >

    <div class="input-toolbar">

      <InputStatusBadge :status="status" />

      <div class="input-decorator">

        <Button
          size="xs"
          type="box"
          mode="naked"
          :icon="togglePassword ? 'hide-password' : 'show-password'"
          :tabindex="-1"
          @click="togglePassword = !togglePassword"
        />

      </div>

    </div>

  </div>

</template>
