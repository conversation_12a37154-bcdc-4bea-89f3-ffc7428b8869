<script setup lang="ts">

import InputStatusBadge from '@lib/components/inputs/input/InputStatusBadge.vue'

import type { SharedInputProps } from '@lib/components/inputs/input/types'

defineOptions({ name: 'TextInput' })

withDefaults( defineProps<SharedInputProps>(), { placeholder: 'Type Here ...' })

const input = defineModel<string>()

</script>

<template>

  <div class="input-block">

    <input
      v-model="input"
      type="text"
      :name="name"
      class="text-input"
      :disabled="disabled"
      :readonly="readonly"
      :tabindex="readonly ? -1 : 0"
      :placeholder="placeholder"
    >

    <div class="input-toolbar">

      <InputStatusBadge :status="status" />

    </div>

  </div>

</template>
