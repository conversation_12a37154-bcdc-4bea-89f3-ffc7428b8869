import { checkValue } from '@lib/scripts/utils'

import type { TextInputValidationOptions } from '@lib/components/inputs/input/text-input/types'
import type { SelectInputValidationOptions } from '@lib/components/inputs/input/select-input/types'
import type { NumberInputValidationOptions } from '@lib/components/inputs/input/number-input/types'
import type { InputType, InputValidationOptions } from '@lib/components/inputs/input/types'

/**
 * Trims the input value.
 * @param value - The value to be trimmed
 * @returns - The trimmed value or null if the value is invalid
 */
export function trimInput( value: string ) {

  if ( !value )
    return null

  const trimmedValue = typeof value === 'string' ? value.trim() : value
  return checkValue( trimmedValue ) ? trimmedValue : null

}

/**
 * Handles errors by filtering out null or undefined values and joining them into a string.
 * @param entries - The entries to be handled
 * @returns - A string of errors or null if there are no errors
 */
function handleErrors( entries: string | string[] ): string | null {

  if ( Array.isArray( entries )) {

    if ( entries.some( e => e ))
      return entries.filter( e => e !== null && e !== undefined ).join( ' ' )

    else return null

  }

  else if ( entries ) {
    return entries
  }

  else { return null }

}

/**
 * Validates the input value against a matching value.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @param match - The value to match against
 * @returns - An object containing the validation result and error message
 */
function matchValue( value: string, name: string, match: string ) {

  if ( !match )
    return { valid: true, error: null }

  if ( value === match )
    return { valid: true, error: null }

  else return { valid: false, error: `${name} doesn\'t match.` }

}

/**
 * Validates the regex input.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @param regex - The regex to be used for validation
 * @returns - An object containing the validation result and error message
 */
function validateRegex( value: string, name: string, regex: RegExp ) {

  if ( value.match( regex ))
    return { valid: true, error: null }

  return { valid: false, error: `${name} is invalid.` }

}

/**
 * Validates the email input.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @returns - An object containing the validation result and error message
 */
function validateEmail( value: string, name: string ) {

  const emailRegex = /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/

  if ( !value.match( emailRegex ))
    return { valid: false, error: `${name} must be a valid email address.` }

  return { valid: true, error: null }

}

/**
 * Validates the multi select input array.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @param minItems - The minimum number of items for the array
 * @param maxItems - The maximum number of items for the array
 * @returns - An object containing the validation result and error message
 */
function validateArrayLength( value: any[], name: string, minItems: number, maxItems: number ) {

  if ( minItems ) {
    if ( value.length < minItems )
      return { valid: false, error: `${name} must have ${minItems} or more items selected.` }
  }

  if ( maxItems ) {
    if ( value.length > maxItems )
      return { valid: false, error: `${name} can't have more than ${maxItems} items selected.` }
  }

  return { valid: true, error: null }

}

/**
 * Validates the text input length.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @param minLength - The minimum length for the input
 * @param maxLength - The maximum length for the input
 * @returns - An object containing the validation result and error message
 */
function validateTextLength( value: string, name: string, minLength: number, maxLength: number ) {

  if ( minLength ) {
    if ( value?.length < minLength )
      return { valid: false, error: `${name} must be ${minLength} or more characters long.` }
  }

  if ( maxLength ) {
    if ( value?.length > maxLength )
      return { valid: false, error: `${name} must be ${maxLength} or less characters long.` }
  }

  return { valid: true, error: null }

}

/**
 * Validates the number input range.
 * @param value - The value to be validated
 * @param name - The name of the input
 * @param min - The minimum value for the input
 * @param max - The maximum value for the input
 * @returns - An object containing the validation result and error message
 */
function validateNumberRange( value: number, name: string, min: number, max: number ) {

  if ( min ) {
    if ( value < min )
      return { valid: false, error: `${name} must be greater than or equal to ${min}.` }
  }

  if ( max ) {
    if ( value > max )
      return { valid: false, error: `${name} must be less than or equal to ${max}.` }
  }

  return { valid: true, error: null }

}

/**
 * Validates the input value based on the input type and validation options.
 * @param value - The value to be validated
 * @param required - Whether the input is required
 * @param inputType - The type of the input ( text, number, currency, textarea, select, etc. )
 * @param inputName - The name of the input
 * @param validationOptions - The validation options for the input
 * @returns - An object containing the validation result and error message
 */
export function validateInput<T extends InputType, M extends boolean>(
  value: any,
  required: boolean,
  inputType: T,
  inputName: string,
  validationOptions: InputValidationOptions<T, M>,
) {

  const name = inputName || 'Input'

  if ( required && !checkValue( value ))
    return { valid: false, error: `${name} is required.` }

  if ( !required && !checkValue( value ))
    return { valid: true, error: null }

  if ( inputType === 'email' )
    return validateEmail( value, name )

  if (
    inputType === 'text'
    || inputType === 'email'
    || inputType === 'password'
    || inputType === 'textarea'
  ) {

    const options     = validationOptions as TextInputValidationOptions
    const matchText   = matchValue( value, name, options?.match )
    const regexText   = validateRegex( value, name, options?.regex )
    const textLength  = validateTextLength( value, name, options?.minLength, options?.maxLength )

    return {
      valid: matchText.valid && regexText.valid && textLength.valid,
      error: handleErrors( [ textLength.error, matchText.error, regexText.error ] )
    }

  }

  if ( inputType === 'number' ) {

    const options           = validationOptions as NumberInputValidationOptions
    const { valid, error }  = validateNumberRange( value, name, options?.min, options?.max )

    return { valid, error }

  }

  if ( inputType === 'select' ) {

    const options           = validationOptions as SelectInputValidationOptions<M>
    const { valid, error }  = validateArrayLength( value, name, options?.minItems, options?.maxItems )

    return { valid, error }

  }

  return { valid: true, error: null }

}
