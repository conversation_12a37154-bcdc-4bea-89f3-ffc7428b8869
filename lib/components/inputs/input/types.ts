import type { CurrencyInputProps } from '@lib/components/inputs/input/currency-input/types'
import type { TextInputValidationOptions } from '@lib/components/inputs/input/text-input/types'
import type { NumberInputProps, NumberInputValidationOptions } from '@lib/components/inputs/input/number-input/types'
import type { SelectInputProps, SelectInputValidationOptions, SelectModelType } from '@lib/components/inputs/input/select-input/types'

export type InputType = 'text' | 'email' | 'password' | 'number' | 'currency' | 'textarea' | 'select'
export type InputStatus = 'error' | 'warning'

/**
 * InputModel
 * @description The model type for the input component.
 * @template T - The type of the input ( text, number, currency, textarea, select, etc. )
 * @template Multiple - Whether the input is multiple ( for select inputs )
 */
export type InputModel<T extends InputType, Multiple extends boolean> =
  T extends 'select'
    ? SelectModelType<Multiple>
    : T extends 'number' | 'currency'
      ? number
      : T extends 'text'
        ? string | number
        : string

export interface SharedInputProps {
  size?:        's' | 'm' | 'l'
  name?:        string
  status?:      InputStatus
  required?:    boolean
  disabled?:    boolean
  readonly?:    boolean
  placeholder?: string
}

/**
 * InputValidationOptions
 * The validation options are based on the input type.
 * @template T - The type of the input ( text, number, currency, textarea, select, etc. )
 * @template M - Whether the input is multiple ( for select inputs )
 * @description Validation options for the input component.
 */
export type InputValidationOptions<T extends InputType, M extends boolean> =
  T extends 'select' ? SelectInputValidationOptions<M>
    : T extends 'text' ? TextInputValidationOptions
      : T extends 'email' ? TextInputValidationOptions
        : T extends 'password' ? TextInputValidationOptions
          : T extends 'currency' ? TextInputValidationOptions
            : T extends 'textarea' ? TextInputValidationOptions
              : T extends 'number' ? NumberInputValidationOptions
                : TextInputValidationOptions

/**
 * InputProps
 * @description Props for the input component
 *
 * Because of limitations of the vue typescript compiler with conditional types,
 * the main input props must contain all of the inputs props, but we can work around this
 * by returning never for the props that are not applicable to the input type.
 *
 * [TODO] - This is a limitation of the vue typescript compiler, and should be fixed in the future.
 *
 * @template T - The type of the input ( text, number, currency, textarea, select, etc. )
 * @template Multiple - Whether the input is multiple ( for select inputs )
 */
export interface InputProps<T extends InputType, Multiple extends boolean> extends
  SharedInputProps,
  SelectInputProps<T, Multiple>,
  NumberInputProps<T, Multiple>,
  CurrencyInputProps<T> {
  type?:              T
  label:              Multiple extends true ? string | boolean : T extends 'textarea' ? string | boolean : string
  valid?:             boolean
  message?:           string
  modelValue?:        InputModel<T, Multiple>
  customError?:       boolean
  validationOptions?: InputValidationOptions<T, Multiple>
}
