@import '@lib/components/inputs/input/select-input/SelectInput.css';
@import '@lib/components/inputs/input/number-input/NumberInput.css';
@import '@lib/components/inputs/input/textarea-input/TextareaInput.css';

.default-design-system {

  .input {

    @apply h-auto;
   
    .label-main-block {
      
      @apply w-full h-auto grid grid-rows-[max-content_1fr];
  
      .label-text-static {
  
        /* 
        This classed can be used to expose a static label 
        text that is relative to the main label block.
  
        This element is hidden by default because in this
        design system we use the dynamic label text.
        */
  
        @apply hidden;

        .label-required-decorator { @apply text-interactive; }
  
      }
  
      .label-nested-block {
  
        @apply 
        w-full h-full relative flex items-center bg-field-01 caret-focus
        after:border-b after:w-full after:h-full after:absolute after:left-0 after:top-0 after:pointer-events-none
        focus-within:after:border-b-2 focus-within:after:border-focus;
  
        .label-input-slot {
  
          @apply w-full h-full relative;
  
          .label-text-dynamic {

            @apply absolute top-1/2 left-4 -translate-y-1/2 pointer-events-none transition-[top_transform] ease-in-out duration-150 text-text-secondary;

            .label-required-decorator { @apply text-interactive; }

          }

          .input-block {

            @apply w-full h-full relative flex;

            .text-input, .email-input, .password-input, .number-input, .currency-input {
              @apply 
              w-full h-full px-4 bg-transparent outline-hidden transition-[padding-top]
              placeholder:text-transparent placeholder:select-none 
              focus:placeholder:text-text-placeholder;
            }

          }

          &.s { @apply min-h-8 text-sm; }
          &.m { @apply min-h-10 text-sm; }  
          &.l { @apply min-h-12; }
  
        }
  
      }

      &.input-has-value {

        .label-nested-block {
      
          .label-input-slot {
    
            .label-text-dynamic {
              @apply top-[0.531rem] translate-y-0 text-[0.625rem] uppercase text-text-secondary;
            }
    
            .number-input, .currency-input {
              @apply font-mono tabular-nums pt-[0.75rem];
            }

            .text-input, .email-input, .password-input {
              @apply pt-[0.75rem];
            }

            &.m { 

              .label-text-dynamic {
                @apply top-[0.2rem];
              }

              .text-input, .email-input, .password-input, .number-input, .currency-input {
                @apply pt-[0.6rem];
              }

            }

            &.s {

              .label-text-dynamic {
                @apply hidden;
              }

              .text-input, .email-input, .password-input, .number-input, .currency-input {
                @apply pt-0;
              }

            }
    
          }
      
          }

      }

      &.input-has-error {

        .label-nested-block {

          @apply 
          caret-support-error 
          after:border-b-2 after:border-support-error
          focus-within:after:border-b-support-error;
          
        }

      }

      &.input-is-disabled {

        .label-nested-block {

          @apply after:border-transparent;

          .label-input-slot {

            .label-text-dynamic {
              @apply text-text-disabled;
            }

            .number-input, .currency-input, .text-input {
              @apply text-text-disabled;
            }

          }

        }

      }

      &:focus-within {

        .label-nested-block {
  
          .label-input-slot {
    
            .label-text-dynamic {
              @apply translate-y-0 top-[0.531rem] text-[0.625rem] uppercase text-text-secondary;
            }

            .text-input, .email-input, .password-input, .number-input, .currency-input {
              @apply pt-[0.75rem] placeholder:text-text-placeholder;
            }

            &.m { 

              .label-text-dynamic {
                @apply top-[0.2rem];
              }

              .text-input, .email-input, .password-input, .number-input, .currency-input {
                @apply pt-[0.6rem];
              }

            }

            &.s {

              .label-text-dynamic {
                @apply hidden;
              }

              .text-input, .email-input, .password-input, .number-input, .currency-input {
                @apply pt-0;
              }

            }
    
          }
          
        }

      }
  
    }

    .input-text-error-message {
      @apply text-xs text-text-error px-4 py-1;
    }

    .input-text-warning-message {
      @apply text-xs text-text-helper px-4 py-1;
    }
  
    .input-text-helper-message {
      @apply text-xs text-text-helper px-4 py-1;
    }

    .input-toolbar {

      @apply 
      h-full px-2 absolute right-0 top-0 flex items-center
      before:w-full before:h-full before:absolute before:right-0 before:bg-field-01 before:mask-alpha before:mask-l-from-80%;

      .input-status {

        @apply size-[1.625rem] relative grid place-content-center not-[:last-child]:separator-r;

        &.input-status-error {
          @apply text-text-error;
        }

      }

      .input-decorator {
        @apply size-[1.625rem] relative grid place-content-center not-[:last-child]:separator-r;
      }

    }
  
  }

}