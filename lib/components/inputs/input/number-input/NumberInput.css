.input {

  &.input-type-number {

    .label-main-block {

      .label-nested-block {

        .label-input-slot {

          .input-block {

            .number-input-controls-bar {

              @apply flex bg-field-01;

              .number-input-control {
                @apply not-[:last-child]:separator-r;
              }

            }

          }

        }

      }

      &.input-has-error, &.input-has-warning {

        .label-input-slot {

          .input-block {

            .number-input-controls-bar {

              .number-input-control {
                @apply not-[:last-child]:separator-l;
              }

            }

          }

        }

      }

    }
  }

}