<script setup lang="ts">

import { computed } from 'vue'
import { checkValue } from '@lib/scripts/utils'

import Button from '@lib/components/button/Button.vue'
import InputStatusBadge from '@lib/components/inputs/input/InputStatusBadge.vue'

import type { NumberInputProps } from '@lib/components/inputs/input/number-input/types'

defineOptions({ name: 'NumberInput' })

const props = withDefaults( defineProps<NumberInputProps<'number', false>>(), {
  step:         1,
  assertive:    true,
  placeholder:  'Type a number ...',
  showControls: true,
})

const emits = defineEmits<{ 'update:numberModel': [ number ] }>()

const input = computed({
  get: () => props.numberModel,
  set: ( value ) => {

    // Cast the value to a number, ensuring it is a valid number.
    // If the value is not a valid number, it will be set to null.

    let numberValue = value

    if ( !checkValue( numberValue )) { // ------------- If the value is not a valid number,
      numberValue = null // --------------------------- Set the number value to null.
    }

    else {

      numberValue
        = props.strict // ----------------------------- If the input is strict,
          ? Number.parseInt( String( value )) // ------ parse the value as an integer,
          : Number.parseFloat( String( value )) // ---- Otherwise as a float.

    }

    emits( 'update:numberModel', numberValue ) // ----- Emit the update event with the number value.

  },
})

/**
 * @param e KeyboardEvent
 * @description Handles the keyboard input for the number input field.
 * Prevents invalid characters and enforces number input rules.
 */
function handleNumberInput( e: KeyboardEvent ) {

  if ( e.metaKey && e.code === 'KeyA' ) // ------------------------------------------------------ Allow text selection with metaKey + KeyA.
    return

  if ( e.code === 'Backspace' )
    return // ----------------------------------------------------------------------------------- Allow backspace key to delete input.

  if ( e.shiftKey ) // -------------------------------------------------------------------------- Prevent input when shift key is pressed
    e.preventDefault()

  let allowedKeys = [ // ------------------------------------------------------------------------ List of allowed keys for number input.
    'Tab',
    'Enter',
    'Delete',
    'ArrowUp',
    'AltLeft',
    'AltRight',
    'MetaLeft',
    'ArrowDown',
    'Backspace',
    'MetaRight',
    'ArrowLeft',
    'ArrowRight',
    'ControlLeft',
    'ControlRight'
  ]

  const strictKeys  = [ 'Comma', 'Minus', 'Period' ] // ----------------------------------------- List of keys that are not allowed for strict number input.

  if ( !props.strict ) // ----------------------------------------------------------------------- If the input is not strict, add the strict keys to the allowed keys.
    allowedKeys = [ ...allowedKeys, ...strictKeys ]

  if ( // --------------------------------------------------------------------------------------- Prevent input if the key is not a digit, numpad or not an allowed key.
    !e.code.match( 'Digit' )
    && !e.code.match( 'Numpad' )
    && !allowedKeys.includes( e.code )
  )
    e.preventDefault()

  const target    = e.target as HTMLInputElement // --------------------------------------------- Get the input target.
  const tValue    = target.value // ------------------------------------------------------------- Get the input target value.
  const lastChar  = tValue.charAt( tValue.length - 1 ) // --------------------------------------- Get the last character of the input value.
  const nextValue = target.value + e.key // ----------------------------------------------------- Calculate the value after input.

  const regex = /^(?!.*\.,|,\.).*$/ // ---------------------------------------------------------- Prevent comma or period next to each other, but allow multiple commas or periods elsewhere.

  if ( !regex.test( nextValue )) // ------------------------------------------------------------- Prevent input if the next value does not match the regex.
    e.preventDefault()

  const dirtyCharacters = [ '.', ',', 'e', 'E' ] // --------------------------------------------- List of dirty characters that should be removed from the input value.

  if ( !!tValue && e.key === '-' ) // ----------------------------------------------------------- Prevent minus sign if the input already has a value.
    e.preventDefault()

  if ( dirtyCharacters.includes( lastChar ) && dirtyCharacters.includes( e.key )) // ------------ Prevent multiple decimal points in the input.
    e.preventDefault()

  const minValue = props?.validationOptions?.min // --------------------------------------------- Get the minimum value from validation options
  const maxValue = props?.validationOptions?.max // --------------------------------------------- Get the maximum value from validation options

  if ( checkValue( minValue )) { // ------------------------------------------------------------- If a minimum value is set, check if the next value is less than the minimum value.

    if ( minValue >= 0 && e.key === '-' ) // ---------------------------------------------------- Prevent negative sign if the minimum value is greater than or equal to 0
      e.preventDefault()

    const minValueLength = String( minValue ).length // ----------------------------------------- Get the length of the minimum value as a string.

    if ( nextValue.length === minValueLength && Number.parseInt( nextValue ) < minValue ) // ---- If the next value is less than the minimum value,
      props.assertive && e.preventDefault() // -------------------------------------------------- prevent input if assertive is true.

  }

  if ( checkValue( maxValue ) && Number.parseInt( nextValue ) > maxValue ) { // ----------------- If a maximum value is set, check if the next value is greater than the maximum value.

    if ( props.assertive ) { // ----------------------------------------------------------------- If assertive is true, prevent input and set the input value to the maximum value.

      e.preventDefault()

      target.value = String( maxValue ) // ------------------------------------------------------ If the next value is greater than the maximum value, set the input value to the maximum value.
      input.value = Number.parseFloat( target.value ) // ---------------------------------------- Update the input model with the maximum value.

    }

  }

}

/**
 * @param e FocusEvent
 * @description Sanitizes the number input by removing any invalid characters.
 * This is called on blur event to ensure the input value is a valid number.
 */
function sanitizeNumberInput( e: FocusEvent ) {

  const target = e.target as HTMLInputElement // ----------------- Get the input target.
  const value  = target.value // --------------------------------- Get the input value.

  const dirtyCharacters = [ ' ', ',', 'e', 'E', '-', '.' ] // ---- List of dirty characters that should be removed from the input value.

  const lastChar = value.charAt( value.length - 1 ) // ----------- Get the last character of the input value.

  if ( dirtyCharacters.includes( lastChar )) { // ---------------- If the last character is a dirty character, remove it from the input value.

    target.value = value.slice( 0, -1 )
    input.value = Number.parseFloat( target.value ) // ----------- Update the input model with the sanitized value.

  }

}

/**
 * @param step number
 * @description Increments the input value by the given step.
 * If a maximum value is set, it will not exceed that value.
 */
function incrementNumber( step: number ) {

  const value = input.value + step // --------------- Get the current input value with the step.

  if ( value > props.validationOptions.max ) // ----- If a maximum value is set, check if the new value exceeds the maximum value.
    input.value = props.validationOptions.max // ---- If it does, set the input value to the maximum value.

  else input.value = value // ----------------------- Otherwise, set the input value to the new value.

}

/**
 * @param step number
 * @description Decrements the input value by the given step.
 * If a minimum value is set, it will not go below that value.
 */
function decrementNumber( step: number ) {

  const value = input.value - step // --------------- Get the current input value with the step.

  if ( value < props.validationOptions.min ) // ----- If a minimum value is set, check if the new value is less than the minimum value.
    input.value = props.validationOptions.min // ---- If it does, set the input value to the minimum value.

  else input.value = value // ----------------------- Otherwise, set the input value to the new value.

}

</script>

<template>

  <div class="input-block">

    <div class="input-block">

      <input
        v-model="input"
        :min="props?.validationOptions?.min"
        :max="props?.validationOptions?.max"
        type="text"
        :name="name"
        class="number-input"
        :disabled="disabled"
        :readonly="readonly"
        :tabindex="readonly ? -1 : 0"
        :placeholder="placeholder"
        @blur="sanitizeNumberInput"
        @keydown="handleNumberInput"
      >

      <div class="input-toolbar number-input-toolbar">

        <InputStatusBadge :status="status" />

      </div>

    </div>

    <div v-if="showControls" class="number-input-controls-bar">

      <Button
        type="box"
        mode="naked"
        icon="minus"
        :size="size"
        class="number-input-control"
        :disabled="input <= props?.validationOptions?.min"
        @click="() => decrementNumber(step)"
      />

      <Button
        type="box"
        icon="add"
        mode="naked"
        :size="size"
        class="number-input-control"
        :disabled="input >= props?.validationOptions?.max"
        @click="() => incrementNumber(step)"
      />

    </div>

  </div>

</template>
