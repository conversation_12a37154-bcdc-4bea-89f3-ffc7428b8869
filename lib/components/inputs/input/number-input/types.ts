import type { InputType, InputValidationOptions, SharedInputProps } from '@lib/components/inputs/input/types'

export interface NumberInputProps<T extends InputType, M extends boolean> extends SharedInputProps {
  step?:              T extends 'number' ? number : never
  strict?:            T extends 'number' ? boolean : never
  assertive?:         T extends 'number' ? boolean : never
  numberModel?:       T extends 'number' ? number : never
  showControls?:      T extends 'number' ? boolean : never
  validationOptions?: InputValidationOptions<T, M>
}

export interface NumberInputValidationOptions {
  min?: number
  max?: number
}
