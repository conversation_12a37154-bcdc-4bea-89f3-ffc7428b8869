<script setup lang="ts">

import type { SharedInputProps } from '@lib/components/inputs/input/types'

interface Props extends SharedInputProps {}

defineOptions({
  name: 'TextareaInput',
})

withDefaults( defineProps<Props>(), { placeholder: 'Type Here ...' })

const input = defineModel<string>()

</script>

<template>

  <textarea
    v-model="input"
    class="textarea-input"
    :name="name"
    :disabled="disabled"
    :readonly="readonly"
    :required="required"
    :placeholder="placeholder"
  />

</template>
