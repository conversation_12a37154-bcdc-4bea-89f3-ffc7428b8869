<script setup lang="ts">

import { watch } from 'vue'
import { CurrencyDisplay, useCurrencyInput } from 'vue-currency-input'

import InputStatusBadge from '@lib/components/inputs/input/InputStatusBadge.vue'

import type { CurrencyInputProps } from '@lib/components/inputs/input/currency-input/types'

defineOptions({ name: 'CurrencyInput' })

const props = withDefaults( defineProps<CurrencyInputProps<'currency'>>(), { currency: 'USD', placeholder: 'Type amount ...' })

defineEmits<{ change: [ value: Event ] }>()

const input = defineModel<number>()

const { inputRef, setValue } = useCurrencyInput({
  currency:                           props.currency,
  useGrouping:                        true,
  accountingSign:                     false,
  currencyDisplay:                    CurrencyDisplay.narrowSymbol,
  autoDecimalDigits:                  true,
  hideCurrencySymbolOnFocus:          false,
  hideGroupingSeparatorOnFocus:       false,
  hideNegligibleDecimalDigitsOnFocus: false
})

watch( input, n => setValue( n ))

</script>

<template>

  <div class="input-block">

    <input
      ref="inputRef"
      type="text"
      :name="name"
      class="currency-input"
      :disabled="disabled"
      :readonly="readonly"
      :tabindex="readonly ? -1 : 0"
      :placeholder="placeholder"
      @change.prevent
    >

    <div class="input-toolbar">

      <InputStatusBadge :status="status" />

    </div>

  </div>

</template>
