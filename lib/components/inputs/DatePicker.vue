<script setup lang="ts">

import { computed, ref } from 'vue'
import { checkValue, formatDate, sanitizeDate } from '@lib/scripts/utils'

import Label from '@lib/components/utils/Label.vue'
import Button from '@lib/components/button/Button.vue'
import Dropdown from '@lib/components/utils/dropdown/Dropdown.vue'
import Calendar from '@lib/components/utils/Calendar.vue'
import ValidationWrapper from '@lib/components/utils/ValidationWrapper.vue'
import ScrollableCalendar from '@lib/components/utils/ScrollableCalendar.vue'

import type { DateModel } from '@lib/types/calendarTypes'
import type { DatePickerProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'DatePickerInput'
})

const props = withDefaults( defineProps<DatePickerProps<DateModel>>(), {
  type:              'date',
  size:              'l',
  mode:              'primary',
  range:             false,
  required:          true,
  nullable:          true,
  teleport:          true,
  returnType:        'UTC-date-time',
  showButton:        true,
  calendarStartFrom: 'sunday'
})

const emits = defineEmits<{
  'update:modelValue': [ value: DateModel ]
}>()

const focus           = ref<boolean>( false )
const element         = ref<HTMLButtonElement>( null )
const toggleList      = ref<boolean>( false )
const yearCounter     = ref<number>( 0 )
const wrapperElement  = ref<HTMLDivElement>( null )

function formatDateByReturnType( date: Date | string, type: typeof props.returnType ): Date | string {

  if ( type === 'date' )
    return new Date( date )

  else return formatDate( date, type )

}

function formatDateByRange( value: DateModel, type: typeof props.returnType ): DateModel {

  if ( Array.isArray( value ))
    return value.map( date => formatDateByReturnType( date, type ))

  return formatDateByReturnType( value, type )

}

function sanitizeInputDate( value: DateModel ): DateModel {

  if ( Array.isArray( value ))
    return value.map( entry => sanitizeDate( entry ))

  return sanitizeDate( value )

}

const input = computed<DateModel>({
  get: () => sanitizeInputDate( props.modelValue ),
  set: value => emits( 'update:modelValue', value ? formatDateByRange( value, props.returnType ) : value )
})

function getCurrentYear( value: DateModel ): number {

  if ( !checkValue( value ))
    return new Date().getFullYear()

  if ( Array.isArray( value ))
    return new Date( value[1] ).getFullYear()

  return new Date( value ).getFullYear()

}

const currentYear = computed(() => getCurrentYear( input.value ) + yearCounter.value )

const block = ref<boolean>( false )

function openList() {

  if ( block.value || props.readonly )
    return

  toggleList.value = !toggleList.value

}

function closeList() {

  block.value = true
  toggleList.value = false

  setTimeout(() => block.value = false, 150 )

}

function displayDate() {

  if ( Array.isArray( input.value )) {

    if ( input.value.length === 0 )
      return 'Select Date'

    if ( input.value.length === 1 )
      return formatDate( input.value[0], 'MMM DD, YYYY' )

    if ( input.value.length === 2 ) {

      if ( new Date( input.value[0] ).getFullYear() === new Date( input.value[1] ).getFullYear())
        return `${formatDate( input.value[0], 'DD MMM' )} - ${formatDate( input.value[1], 'MMM DD, YYYY' )}`

      else return `${formatDate( input.value[0], 'MMM DD, YYYY' )} - ${formatDate( input.value[1], 'MMM DD, YYYY' )}`

    }

  }

  else {
    return input.value ? formatDate( input.value, 'MMM DD, YYYY' ) : props.label && props.size === 'l' ? props.placeholder ?? props.label ?? props.name ?? 'Select Date' : null
  }

}

const canRemoveInput = computed(() => props.nullable && checkValue( input.value ))

function removeInput( e: PointerEvent ) {

  if ( canRemoveInput.value ) {
    e.stopPropagation()
    input.value = null
  }

}

</script>

<template>

  <ValidationWrapper
    v-bind="props"
    @expose-element="(el) => wrapperElement = el"
  >

    <div
      class="w-full flex cursor-pointer"
      :class="{
        'h-10': size === 'm',
        'h-auto': size === 'auto',
        'h-[3.375rem]': size === 'l',
        'input-disabled': disabled,
        'input-mode-naked': mode === 'naked',
        'input-mode-ghost': mode === 'ghost',
        'input-mode-primary': mode === 'primary',
      }"
    >

      <slot name="prefix" />

      <Label
        :name="name"
        :mode="mode"
        :size="size"
        :input="input"
        :label="label"
        :required="required"
        :force-focus="!!label && size === 'l' && toggleList"
        :placeholder="placeholder"
        @click.stop="openList"
      >
        <button
          ref="element"
          class="truncate w-full h-full px-4 flex items-center outline-hidden"
          @focus="focus = true"
          @blur="focus = false"
        >
          <p
            class="truncate"
            :class="{
              'opacity-0': !focus && !toggleList && !checkValue(input),
              'text-core-60': (focus || toggleList) && !checkValue(input),
            }"
            v-html="displayDate()"
          />
        </button>

        <template #icon>

          <div
            v-if="showButton && !readonly"
            class="h-full grid place-content-center"
            :class="{
              'px-2': size === 'l',
            }"
          >

            <Button
              size="m"
              type="box"
              mode="ghost"
              :icon="canRemoveInput ? 'close' : 'calendar'"
              :tabindex="canRemoveInput ? 0 : -1"
              @click="removeInput"
              @focus="focus = true"
              @blur="focus = false"
            />

          </div>

        </template>

      </Label>

      <slot name="suffix" />

      <Dropdown
        v-if="toggleList"
        :parent-element="wrapperElement"
        @close="closeList"
      >

        <Calendar
          v-model="input"
          :range="range"
          :limit-to="limitTo"
          :limit-from="limitFrom"
          :start-from="calendarStartFrom"
          class="hidden md:flex"
        />

        <template #header>

          <div class="w-full h-12 sticky top-0 z-1 pl-6 flex items-center bg-core-20 border-b border-core-30 ignore-outside-droplist">

            <p class="text-sm font-semibold grow">
              {{ $t('global.label.calendar') }}
            </p>

            <Button
              size="m"
              type="box"
              mode="ghost"
              icon="chevron-left"
              @click="() => yearCounter -= 1"
            />

            <p class="text-sm font-medium pr-2">
              {{ currentYear }}
            </p>

            <Button
              size="m"
              type="box"
              mode="ghost"
              icon="chevron-right"
              @click="() => yearCounter += 1"
            />

            <Button
              size="m"
              type="box"
              mode="ghost"
              icon="close"
              @click="closeList"
            />

          </div>

        </template>

        <div class="w-full h-full max-h-[calc(100%-3rem)]">

          <ScrollableCalendar
            v-model="input"
            :range="range"
            :limit-to="limitTo"
            :limit-from="limitFrom"
            :start-from="calendarStartFrom"
            :year-counter="yearCounter"
            class="md:hidden"
            @close="closeList"
          />

        </div>

      </Dropdown>

    </div>

  </ValidationWrapper>

</template>
