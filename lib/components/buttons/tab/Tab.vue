<script setup lang="ts">

import { ref } from 'vue'
import { RouterLink } from 'vue-router'
import { useEventListener } from '@vueuse/core'
import { focusFirstOptionOnKeyDown } from '@lib/components/utils/dropdown/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Droplist from '@lib/components/utils/dropdown/Dropdown.vue'

import type { IconName } from '@lib/store/icon'
import type { RouteLocationRaw } from 'vue-router'

const props = withDefaults(
  defineProps<{
    to?:                   RouteLocationRaw
    size?:                 's' | 'm' | 'l' | 'auto'
    type?:                 'box' | 'rect'
    icon?:                 IconName
    label?:                string
    active?:               boolean
    options?:              DropListOption[]
    disabled?:             boolean
    modifier?:             Omit<GlobalModifiers, 'high-contrast' | 'low-contrast'>
    toggleOptionsTrigger?: 'click' | 'hover'
  }>(),
  {
    type:                 'rect',
    size:                 'm',
    toggleOptionsTrigger: 'click'
  }
)

const emits = defineEmits<{
  click: [ Event: MouseEvent ]
}>()

const tabElement    = ref<HTMLButtonElement>( null )
const toggleOptions = ref<boolean>( false )

function closeList() {
  toggleOptions.value = false
  tabElement.value.focus()
}

/**
 * Toggles the options droplist.
 */

function openList( e?: MouseEvent, open = !toggleOptions.value ) {

  if ( e )
    emits( 'click', e )

  if ( props.options ) {

    e?.preventDefault()
    toggleOptions.value = open

  }

}

/**
 * Sets the focus on the first option in the list when a trigger key is clicked.
 * @param {KeyboardEvent} event - The keyboard event.
 */

function focusFirstOption( event: KeyboardEvent ) {
  const { open } = focusFirstOptionOnKeyDown( event, tabElement.value, toggleOptions.value )
  openList( null, open )
}

useEventListener( tabElement, 'keydown', focusFirstOption )

</script>

<template>

  <component
    :is="props.to ? RouterLink : 'button'"
    ref="tabElement"
    :to="to"
    class="tab ignore-outside-droplist"
    :class="[
      {
        's': size === 's',
        'm': size === 'm',
        'l': size === 'l',
        'disabled': props.disabled,
        'tab-active': active || toggleOptions,
      },
      type,
      modifier,
    ]"
    active-class="tab-active"
    @click="openList"
  >

    <slot :active="active || toggleOptions">
      <span v-if="props.label" class="tab-label">{{ props.label }}</span>
      <Icon v-if=" props.icon" class="tab-icon" :name="icon" size="m" />
    </slot>

    <Droplist
      v-if="toggleOptions"
      :input="null"
      :teleport="toggleOptionsTrigger === 'click'"
      :parent-element="tabElement"
      :options="options"
      @close="toggleOptionsTrigger !== 'hover' && closeList()"
      @selected="closeList()"
    >
      <slot name="droplist" />
    </Droplist>

  </component>

</template>
