<script setup lang="ts">

import { onClickOutside } from '@vueuse/core'
import { computed, ref, watch } from 'vue'

type Offset = `${string}px` | `${string}rem` | number

interface Props {
  dim?:            boolean
  open:            boolean
  name?:           string
  zIndex?:         number
  strict?:         boolean
  offsetX?:        Offset
  offsetY?:        Offset
  position?:       'left' | 'right' | 'bottom'
  fitContent?:     boolean
  customClass?:    string
  customTwOffset?: string
}

interface Emits {
  ( eventName: 'close' ): void
}

const props = withDefaults( defineProps<Props>(), {
  zIndex:     20,
  strict:     false,
  position:   'right',
  fitContent: true,
})

const emits = defineEmits<Emits>()

function closeSidebarOnClickOutside() {

  if ( props.strict )
    return

  emits( 'close' )

}

const sidebarElement = ref<HTMLElement>( null )

const openSidebar     = ref( false )
const sidebarOffsetX  = computed(() => props.offsetX ? typeof props.offsetX === 'string' ? props.offsetX : `${props.offsetX}px` : '0px' )
const sidebarOffsetY  = computed(() => props.offsetY ? typeof props.offsetY === 'string' ? props.offsetY : `${props.offsetY}px` : '0px' )

onClickOutside( sidebarElement, closeSidebarOnClickOutside, { ignore: [ '.ignore-outside', '.ignore-sidebar' ] })

watch(() => props.open, ( n ) => {

  if ( n )
    openSidebar.value = true

  else
    setTimeout(() => openSidebar.value = false, 300 )

}, { immediate: true })

</script>

<template>

  <Teleport to="#app">

    <div
      v-if="openSidebar"
      class="fixed top-0 left-0 w-full isolate"
      :class="[
        {
          'bg-core-90/30 backdrop-blur-xs': dim,
          'ignore-sidebar': strict,
        },
        customClass,
        customTwOffset,
      ]"
      :style="{
        zIndex,
        top: customTwOffset ? 'none' : sidebarOffsetY,
        height: customTwOffset ? 'none' : `calc(100% - ${sidebarOffsetY})`,
      }"
    >

      <Transition
        :name="position === 'left' ? 'sidebar-modal-left' : position === 'right' ? 'sidebar-modal-right' : position === 'bottom' ? 'sidebar-modal-bottom' : ''"
        mode="out-in"
        appear
      >

        <div
          v-if="open"
          ref="sidebarElement"
          class="absolute border-core-30 bg-core-20 ignore-outside"
          :class="{
            'w-full h-full border-none': !fitContent && position !== 'bottom',
            'md:w-max md:max-w-full md:border-t-0 w-full h-full border-t': fitContent && position !== 'bottom',
            'md:border-r': position === 'left',
            'md:border-l': position === 'right',
            'w-full border-t h-auto max-h-[80%] overflow-hidden overflow-y-auto': fitContent && position === 'bottom',
            'w-full border-t h-full max-h-full': !fitContent && position === 'bottom',
          }"
          :style="{
            left: position === 'left' ? sidebarOffsetX : undefined,
            right: position === 'right' ? sidebarOffsetX : undefined,
            bottom: position === 'bottom' ? customTwOffset ? 'none' : sidebarOffsetY : undefined,
          }"
        >

          <slot />

        </div>

      </Transition>

    </div>

  </Teleport>

</template>
