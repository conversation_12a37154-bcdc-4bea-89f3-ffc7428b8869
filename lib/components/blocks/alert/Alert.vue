<script setup lang="ts">

import { alertQueue } from '@lib/store/snackbar'
import { vOnClickOutside } from '@vueuse/components'
import { computed, ref, watch } from 'vue'

import Button from '@lib/components/button/Button.vue'
import Snackbar from '@lib/components/utils/snackbar/Snackbar.vue'

const isEmpty        = ref( true )
const mouseOver      = ref( false )
const isExpanded     = ref( false )
const reversedAlerts = computed(() => [ ...alertQueue.value ].reverse())

/**
 * Expands the notification list if it’s currently collapsed and there are multiple alerts.
 * @param {MouseEvent} event - The click event.
 */
function toggleExpand( event: MouseEvent ) {
  if ( !isExpanded.value && alertQueue.value.length > 1 ) {
    isExpanded.value = true
    event.stopPropagation()
  }
}

function clearAll() {

  const interval = 300 / ( alertQueue.value.length * 2 )

  const alertsInterval = setInterval(() => {

    if ( alertQueue.value.length > 0 ) {
      alertQueue.value.pop()
    }

    else {

      setTimeout(() => {
        isExpanded.value = false
        isEmpty.value = true
      }, 300 )
      clearInterval( alertsInterval )

    }

  }, interval )

}

/**
 * Computes the style object for a single alert container based on expansion state.
 * @param isExpanded - Whether the notification list is expanded.
 * @param alertQueueLength - Total number of alerts in the queue.
 * @param index - Index of the current alert in the reversed list.
 * @returns The style object for the alert container.
 */

function getAlertContainerStyle( isExpanded: boolean, alertQueueLength: number, index: number ): Record<string, string | number> {

  if ( isExpanded ) {
    return {
      position: 'relative', // ---------------- Stack alerts vertically
      zIndex:   alertQueueLength - index, // -- Higher zIndex for newer alerts
    }
  }

  return {
    position:        'absolute', // --------------------------- Stack at bottom
    left:            0,
    bottom:          index > 0 ? `-${index * 6}px` : '0', // -- Offset each alert slightly downward
    zIndex:          alertQueueLength - index, // ------------- Higher zIndex for newer alerts
    scale:           `${1 - index * 0.03}`, // ---------------- Slightly shrink each alert for stacking effect
    width:           '100%', // ------------------------------- Take full container width
    transformOrigin: 'bottom center', // ---------------------- Animate from bottom center
  }

}

/*
 * Without this watcher, when the last alert is closed, it will not have a transition.
 * This is because the container is removed from the DOM immediately.
*/

watch(() => alertQueue.value?.length, ( newLength, oldLength ) => {
  // Handle the case when the last alert is removed
  if ( oldLength === 1 && newLength === 0 ) {

    setTimeout(() => {
      isEmpty.value = true
      isExpanded.value = false
    }, 300 )
  }
  // Handle the case when alerts are added
  else if ( newLength >= 1 ) {
    isEmpty.value = false
  }
})

</script>

<template>

  <div
    v-on-click-outside="() => isExpanded = false"
    class="alert-container"
    :class="{
      collapsed: !isExpanded,
      expanded: isExpanded,
      empty: isEmpty,
    }"
    @mouseover="mouseOver = true"
    @mouseleave="mouseOver = false"
  >

    <div
      class="alert-inner-container"
      :class="{
        collapsed: !isExpanded,
        expanded: isExpanded,
      }"
      @click="toggleExpand"
    >

      <!-- Clear all bar - only shown when expanded -->
      <Transition>

        <div v-if="isExpanded" class="clear-all-bar-container">
          <!-- Notifications count -->
          <div class="notifications-count-container">
            <div class="notifications-count">
              {{ alertQueue.length }}
            </div>
            <span>Notifications</span>
          </div>

          <div class="bar-buttons-container">

            <Button
              size="m"
              mode="naked"
              title="Clear All"
              @click.stop="clearAll"
            >
              Clear All
            </Button>

            <Button
              size="m"
              type="box"
              mode="naked"
              icon="chevron-up"
              title="Collapse"
              class="separator-l"
              @click.stop="isExpanded = false"
            />

          </div>
        </div>

      </Transition>

      <!-- Container for notifications -->
      <div
        class="notifications-container"
        :class="{ expanded: isExpanded }"
      >

        <!-- TransitionGroup handles both collapsed and expanded views -->
        <TransitionGroup
          name="stack"
          tag="div"
          class="transition-group"
          :class="{
            expanded: isExpanded,
          }"
        >

          <div
            v-for="(alert, index) in reversedAlerts"
            :key="alert.id"
            class="single-snackbar-container"
            :class="{
              'hidden-collapsed-notification': index >= 3 && !isExpanded,
            }"
            :style="getAlertContainerStyle(isExpanded, alertQueue.length, index)"
          >
            <Snackbar
              modifier="inverse"
              v-bind="alert"
              type="alert"
              :pause="isExpanded || mouseOver"
              :strict="!!alert.actionName || !!alert.inlineActionName"
            />
          </div>

        </TransitionGroup>

      </div>
    </div>

  </div>

</template>
