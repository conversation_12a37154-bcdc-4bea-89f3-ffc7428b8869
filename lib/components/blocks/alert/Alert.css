.default-design-system {

.alert-container {

    @apply md:w-[28rem] w-full fixed md:bottom-[2.75rem] bottom-[4.875rem] left-1/2 transform -translate-x-1/2 z-1000 transition-all;
    
    &.collapsed {
        @apply px-4;
    }

    &:not(.collapsed) {
        @apply md:max-h-[calc(100vh-12.5rem)] max-h-[calc(100vh-4.75rem)] md:rounded-b-md bg-background md:shadow-[0px_2px_6px_0px_rgba(0,0,0,0.3)] overflow-hidden overflow-y-auto;
    }

    &.empty {
        @apply h-0 pointer-events-none opacity-0;
    }
    
}

/* Inner container */
.alert-inner-container {

    @apply w-full;

    &.collapsed {
        @apply hover:cursor-pointer;
    }

    &.expanded {
        @apply h-full;
    }

}

/* Bar styles */
.clear-all-bar-container {
    @apply w-full h-10 sticky top-0 z-100 bg-background flex justify-between border-b border-b-border-subtle-00;
}

.notifications-count-container {

    @apply flex items-center gap-1.5 pl-4;

    .notifications-count {
        @apply px-3 py-0.5 text-xs bg-notification-error-background text-support-error rounded-2xl;
    }

    > span {
        @apply font-medium text-sm;
    }

}

/* Buttons styles */
.bar-buttons-container {
    @apply text-sm flex items-center;
}

/* Notifications container */
.notifications-container {
    @apply relative;

    &.expanded {
        @apply h-full md:rounded-b-md;
    }
}

.transition-group {

    @apply relative;

    &.expanded {
        @apply flex flex-col gap-2 p-4;
    }

}
.single-snackbar-container {
    @apply transition-all duration-300 ease-in-out;
}

.hidden-collapsed-notification {
    @apply opacity-0;
}

/* Transitions */

.stack-enter-from {
    @apply opacity-0 translate-y-2.5 scale-95;
  }
    
  /* When snackbar appears */
  .stack-leave-to {
    @apply opacity-0 -translate-x-5 scale-95;
  }
    
  /* Without this, the notifications transition pulls to the left when clear all is pressed */
  .empty .stack-leave-to {
    @apply translate-x-0 scale-50;
  }
  
}