<script setup lang="ts">

import { computed, ref } from 'vue'
import { useEventListener } from '@vueuse/core'
import { focusFirstOptionOnKeyDown } from '@lib/components/utils/dropdown/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Dropdown from '@lib/components/utils/dropdown/Dropdown.vue'

import type { IconName, IconSize } from '@lib/store/icon'
import type { ButtonModifiers, ButtonSize } from '@lib/components/button/types'

const props = withDefaults( defineProps<{

  name?:     string
  size?:     ButtonSize
  type?:     'rect' | 'pill' | 'box' | 'badge'
  mode?:     'primary' | 'secondary' | 'tertiary' | 'ghost' | 'naked'
  icon?:     IconName
  testId?:   string
  options?:  DropListOption[]
  pending?:  boolean
  iconSize?: keyof IconSize
  modifier?: LooseAutoComplete<ButtonModifiers | GlobalModifiers> | LooseAutoComplete<ButtonModifiers | GlobalModifiers>[]
  disabled?: boolean
  tabindex?: number

}>(), {

  size:     'l',
  type:     'rect',
  mode:     'primary',
  iconSize: 'm',
  tabindex: 0

})

const emits = defineEmits<{
  click: [MouseEvent]
}>()

const buttonElement   = ref<HTMLButtonElement>( null ) // ---------------------------------------------------------------------------------- Reference to the button element.
const toggleOptions   = ref<boolean>( false ) // ------------------------------------------------------------------------------------------- Toggles the options droplist.
const buttonModifiers = computed(() => props.modifier ? Array.isArray( props.modifier ) ? props.modifier : [ props.modifier ] : [] ) // ---- List of button modifiers.

/**
 * Toggles the options droplist.
 */

function openList( e?: MouseEvent, open = !toggleOptions.value ) {

  if ( e )
    emits( 'click', e )

  if ( props.options ) {

    e?.preventDefault()
    toggleOptions.value = open

  }

}

/**
 * Closes the options droplist.
 * Focuses the button element when the list is closed.
 */

function closeList() {
  toggleOptions.value = false
  buttonElement.value.focus()
}

/**
 * Sets the focus on the first option in the list when a trigger key is clicked.
 * @param {KeyboardEvent} event - The keyboard event.
 */

function focusFirstOption( event: KeyboardEvent ) {

  if ( !props.options )
    return

  const { open } = focusFirstOptionOnKeyDown( event, buttonElement.value, toggleOptions.value )
  openList( null, open )

}

useEventListener( buttonElement, 'keydown', focusFirstOption )

</script>

<template>

  <button
    ref="buttonElement"
    type="button"
    class="button ignore-outside-droplist"
    :name="name"
    :tabindex="tabindex"
    :disabled="disabled"
    :data-test="testId ? testId : null"
    :class="[...buttonModifiers, {

      pending,
      loading: pending,

      // Button sizes

      l: size === 'l',
      m: size === 'm',
      s: size === 's',
      xs: size === 'xs',

      // Button types

      box: type === 'box',
      pill: type === 'pill',
      rect: type === 'rect',
      badge: type === 'badge',

      // Button modes

      naked: mode === 'naked',
      ghost: mode === 'ghost',
      primary: mode === 'primary',
      tertiary: mode === 'tertiary',
      secondary: mode === 'secondary',

    }]"
    @click="openList"
  >

    <slot :active="toggleOptions">
      <Icon :name="icon" :size="iconSize" class="button-icon" />
    </slot>

    <Dropdown
      v-if="toggleOptions"
      :options="options"
      :parent-element="buttonElement"
      @close="closeList"
      @selected="closeList"
    >

      <slot name="droplist" />

    </Dropdown>

  </button>

</template>
